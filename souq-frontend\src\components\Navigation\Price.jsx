import React, { useEffect, useRef, useState } from "react";
import { FaTimes } from "react-icons/fa";
import { useDispatch } from "react-redux";
import { setPrice } from "../../redux/slices/FilterSlice";
import { LuChevronDown, LuChevronUp } from "react-icons/lu";

export default function PriceSelector() {
  const dispatch = useDispatch()
  const [open, setOpen] = useState(false);
  const [priceFrom, setPriceFrom] = useState("");
  const [priceTo, setPriceTo] = useState("");
  const [selectedPrice, setSelectedPrice] = useState(null);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const reset = () => {
    setPriceFrom("");
    setPriceTo("");
    setSelectedPrice(null);
  };

  const applyPrice = () => {
    if (priceFrom === "" && priceTo === "") {
      setSelectedPrice(null);
    } else {
      dispatch(setPrice(`${priceFrom || "0"} - ${priceTo || "0"}`));
      setSelectedPrice(`${priceFrom || "0"} - ${priceTo || "0"}`);
    }
    setOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setOpen(!open)}
        className="border px-4 py-2 rounded-full bg-white shadow flex items-center gap-2"
      >
        Price
        {open ? (
          <LuChevronUp className="w-5 h-5 text-gray-600" />
        ) : (
          <LuChevronDown className="w-5 h-5 text-gray-600" />
        )}
      </button>
      {open && (
        <div className="absolute z-10 bg-white shadow-lg rounded mt-2 w-64 p-4">
          <div className="flex flex-col gap-3">
            <label className="flex flex-col text-sm">
              From
              <input
                type="number"
                min="0"
                value={priceFrom}
                onChange={(e) => setPriceFrom(e.target.value)}
                placeholder="Min price"
                className="border rounded px-3 py-1 mt-1"
              />
            </label>

            <label className="flex flex-col text-sm">
              To
              <input
                type="number"
                min="0"
                value={priceTo}
                onChange={(e) => setPriceTo(e.target.value)}
                placeholder="Max price"
                className="border rounded px-3 py-1 mt-1"
              />
            </label>

            <div className="flex justify-between mt-4">
              <button
                onClick={reset}
                className="px-4 py-2 border rounded bg-gray-100 hover:bg-gray-200"
              >
                Reset
              </button>
              <button
                onClick={applyPrice}
                className="px-4 py-2 bg-teal-600 text-white rounded hover:bg-teal-700"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}

      {/* {selectedPrice && (
        <button
          className="border px-4 py-2 rounded-full bg-white shadow mt-3 flex items-center min-w-[100px]"
          onClick={reset}
        >
          <span>{selectedPrice}</span>
          <FaTimes className="ml-2 cursor-pointer text-gray-600" size={16} />
        </button>
      )} */}
    </div>
  );
}
