import React, { useState } from 'react';
import { FaApple, FaFacebookSquare } from 'react-icons/fa';
import { FcGoogle } from 'react-icons/fc';
import { X } from 'lucide-react';
import { useAppContext } from '../../context/AppContext';
import LoginModal from './LoginModal';

const AuthModal = () => {
  const { isAuthModalOpen, setIsAuthModalOpen, authMode, setAuthMode, setShowEmailLogin, showSignUp, setShowSignUp } = useAppContext();
  // const [showEmailLogin, setShowEmailLogin] = useState(false);

  if (!isAuthModalOpen) return null;

  const loginModal = () => {
    setIsAuthModalOpen(false)
    setShowEmailLogin(true)
  }

  const signUpModal = () => {
    setIsAuthModalOpen(false)
    setShowSignUp(true)
  }

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
          <button
            onClick={() => setIsAuthModalOpen(false)}
            className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>

          <div className="p-6">
            <h2 className="text-2xl md:text-3xl font-semibold mb-6 text-center mt-4">
              {authMode !== 'login'
                ? 'Join and sell pre-loved clothes with no fees'
                : 'Welcome back'}
            </h2>

            <div className="space-y-4">
              <button
                className="flex items-center justify-center w-full py-2.5 px-4 border border-gray-300 rounded-lg hover:bg-gray-100 transition duration-200"
                onClick={() => {
                  window.location.href = `${import.meta.env.VITE_API_BASE_URL}/api/user/auth/google`;
                }}
              >
                <FcGoogle size={22} className="mr-2" />
                <span>{authMode === 'login' ? 'Continue with Google' : 'Sign up with Google'}</span>
              </button>
              <button className="flex items-center justify-center w-full py-2.5 px-4 border border-gray-300 rounded-lg hover:bg-gray-100 transition duration-200">
                <FaApple size={22} className="text-black mr-2" />
                <span>{authMode === 'login' ? 'Continue with Apple' : 'Sign up with Apple'}</span>
              </button>
              <button className="flex items-center justify-center w-full py-2.5 px-4 border border-gray-300 rounded-lg hover:bg-gray-100 transition duration-200"
                onClick={() => {
                  window.location.href = `${import.meta.env.VITE_API_BASE_URL}/api/user/auth/facebook`;
                }}>
                <FaFacebookSquare size={22} className="text-blue-600 mr-2 rounded-full" />
                <span>{authMode === 'login' ? 'Continue with Facebook' : 'Sign up with Facebook'}</span>
              </button>
            </div>

            <div className="mt-4 text-center text-gray-700 text-base">
              {authMode === 'login' ? (
                <p>
                  Log in with{" "}
                  <button
                    className="text-teal-600 hover:text-teal-700 underline font-medium"
                    onClick={loginModal}
                  >
                    Email
                  </button>
                </p>) : (
                <p>
                  Or register with{" "}
                  <button
                    className="text-teal-600 hover:text-teal-700 underline font-medium"
                    onClick={signUpModal}
                  >
                    Email
                  </button>
                </p>

              )}
            </div>

            <div className="mt-2 text-center text-gray-700 text-base">
              {authMode === 'login' ? (
                <p>
                  Don’t have an account yet?{' '}
                  <button
                    onClick={() => setAuthMode('signup')}
                    className="text-teal-600 hover:text-teal-700 font-medium underline"
                  >
                    Sign up
                  </button>
                </p>
              ) : (
                <p>
                  Already have an account?{' '}
                  <button
                    onClick={() => setAuthMode('login')}
                    className="text-teal-600 hover:text-teal-700 font-medium underline"
                  >
                    Log in
                  </button>
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AuthModal;
