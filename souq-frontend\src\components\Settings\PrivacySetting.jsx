import React, { useState } from "react";
import { Switch } from "@headlessui/react";
import { FaChevronRight } from "react-icons/fa6";


const PrivacySettings = () => {
    const [toggles, setToggles] = useState({});

    const privacyOptions = [
        {
            label:
                "Feature my items in marketing campaigns for a chance to sell faster",
            desc:
                "This allows <PERSON><PERSON> to showcase my items on social media and other websites. The increased visibility could lead to quicker sales.",
        },
        {
            label: "Notify owners when I favorite their items",
        },
        {
            label: "Allow third-party tracking",
        },
        {
            label:
                "Allow Vinted to personalize my feed and search results by evaluating my preferences, settings, previous purchases and usage of Vinted website and app",
        },
        {
            label:
                "Allow Vinted to display my recently viewed items on my Homepage.",
            desc:
                "If you turn this option off but allow personalized content, these items will still be used to personalize your Feed.",
        },
    ];

    const toggleSwitch = (label) => {
        setToggles({ ...toggles, [label]: !toggles[label] });
    };

    return (
        <div className="space-y-6">
              <h2 className="text-xl font-semibold">Privacy settings</h2>
            {privacyOptions.map(({ label, desc }) => (
                <div key={label} className="border p-4 rounded-md shadow-sm">
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="font-medium">{label}</p>
                            {desc && <p className="text-sm text-gray-500 mt-1">{desc}</p>}
                        </div>
                        <Switch
                            checked={!!toggles[label]}
                            onChange={() => toggleSwitch(label)}
                            className={`${toggles[label] ? "bg-teal-600" : "bg-gray-300"} relative inline-flex h-6 w-11 items-center rounded-full mt-1`}
                        >
                            <span className="sr-only">Toggle</span>
                            <span
                                className={`${toggles[label] ? "translate-x-6 rtl:-translate-x-6" : "translate-x-1 rtl:-translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`}
                            />
                        </Switch>
                    </div>
                </div>
            ))}
            <div className="border p-4 rounded-md shadow-sm cursor-pointer">
                <div className="flex justify-between items-center">
                    <div>
                        <p className="font-medium">Download account data</p>
                        <p className="text-sm text-gray-500 mt-1">
                            Request a copy of your Vinted account data.
                        </p>
                    </div>
                    <FaChevronRight className="text-gray-500 text-lg" />
                </div>
            </div>
        </div>
    );
};

export default PrivacySettings;