import React, { useState } from "react";
import { Switch } from "@headlessui/react";

const NotificationSettings = () => {
    const [emailEnabled, setEmailEnabled] = useState(true);
    const notifications = {
        news: ["Vinted Updates", "Marketing communications"],
        high: ["New messages", "New feedback", "Discounted items"],
        other: ["Favorited Items", "New followers", "New items"],
    };
    return (
        <div className="space-y-6">
            <div className="flex justify-between">
                <h2 className="text-xl font-semibold">Enable email notifications</h2>
                <Switch
                    checked={emailEnabled}
                    onChange={setEmailEnabled}
                    className={`${emailEnabled ? "bg-teal-600" : "bg-gray-300"} relative inline-flex h-6 w-11 items-center rounded-full`}
                >
                    <span className="sr-only">Enable</span>
                    <span
                        className={`${emailEnabled ? "translate-x-6 rtl:-translate-x-6" : "translate-x-1 rtl:-translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`}
                    />
                </Switch>
            </div>

            {emailEnabled && (
                <div className="space-y-4">
                    <div>
                        <h3 className="text-lg font-medium mb-3">News</h3>
                        <div className="grid gap-2 md:grid-cols-1">
                            {notifications.news.map((item) => (
                                <SettingToggle key={item} label={item} />
                            ))}
                        </div>
                    </div>

                    <div>
                        <h3 className="text-lg font-medium mb-3">High-priority notifications</h3>
                        <div className="grid gap-2 md:grid-cols-1">
                            {notifications.high.map((item) => (
                                <SettingToggle key={item} label={item} />
                            ))}
                        </div>
                    </div>

                    <div>
                        <h3 className="text-lg font-medium mb-3">Other notifications</h3>
                        <div className="grid gap-2 md:grid-cols-1">
                            {notifications.other.map((item) => (
                                <SettingToggle key={item} label={item} />
                            ))}
                        </div>
                    </div>
                    <div className="border p-2 rounded-md shadow-sm flex justify-between items-center">
                        <span className="ml-1">
                            Set a daily limit for each notification type
                        </span>
                        <select className="border p-2 rounded-md max-w-xs text-sm text-gray-700">
                            <option>Up to 2 notifications</option>
                            <option>Up to 5 notifications</option>
                            <option>No limit</option>
                        </select>
                    </div>
                </div>
            )}
        </div>
    );
};

const SettingToggle = (props) => {
    const [enabled, setEnabled] = useState(true);
    return (
        <div className="flex justify-between items-center border p-3 rounded-md bg-white shadow-sm">
            <span>{props.label}</span>
            <Switch
                checked={enabled}
                onChange={setEnabled}
                className={`${enabled ? "bg-teal-600" : "bg-gray-300"} relative inline-flex h-6 w-11 items-center rounded-full`}
            >
                <span className="sr-only">Toggle</span>
                <span
                    className={`${enabled ? "translate-x-6 rtl:-translate-x-6" : "translate-x-1 rtl:-translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`}
                />
            </Switch>
        </div>
    );
};
export default NotificationSettings;