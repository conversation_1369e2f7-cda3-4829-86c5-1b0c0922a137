import React, { useState } from 'react';
import ProductChatButton from '../components/ProductChatButton';

// Example of how to integrate chat into a product detail page
export default function ProductDetailExample() {
  const [currentUser] = useState({
    id: '6836b8bc2caa73f5098bb68d',
    userName: 'john_doe',
    firstName: 'John',
    lastName: 'Doe'
  });

  const [product] = useState({
    id: '68497b9d588aec0773fc41ff',
    title: 'Men Regular Fit Striped Button Down Collar Casual Shirt',
    description: 'Elevate your casual wardrobe with this Men\'s Regular Fit Striped Button Down Collar Casual Shirt.',
    price: 8,
    shipping_cost: 3,
    brand: 'Zara',
    size: 'XL',
    condition: 'like new',
    colors: 'Brown',
    material: 'Cotton',
    photos: [
      'uploads/products/shirt1.jpeg',
      'uploads/products/shirt2.jpeg'
    ],
    seller: {
      id: '684fe85902ddeb449627dfd5',
      userName: 'fashion_seller',
      firstName: 'Sarah',
      lastName: '<PERSON>',
      profile: 'uploads/profiles/sarah.jpg'
    }
  });

  const isOwnProduct = currentUser.id === product.seller.id;

  return (
    <div style={{ 
      maxWidth: '1200px', 
      margin: '0 auto', 
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Breadcrumb */}
      <div style={{ 
        marginBottom: '20px', 
        fontSize: '14px', 
        color: '#666' 
      }}>
        <span>Home</span> / <span>Men</span> / <span>Clothing</span> / <span>Shirts</span> / <span>Casual Shirt</span>
      </div>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr', 
        gap: '40px',
        '@media (max-width: 768px)': {
          gridTemplateColumns: '1fr'
        }
      }}>
        {/* Product Images */}
        <div>
          <div style={{ 
            width: '100%', 
            height: '500px', 
            backgroundColor: '#f5f5f5',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '20px'
          }}>
            <img 
              src="/api/placeholder/500/500" 
              alt={product.title}
              style={{ 
                maxWidth: '100%', 
                maxHeight: '100%', 
                objectFit: 'cover',
                borderRadius: '8px'
              }}
            />
          </div>
          
          {/* Thumbnail images */}
          <div style={{ 
            display: 'flex', 
            gap: '10px',
            overflowX: 'auto'
          }}>
            {product.photos.map((photo, index) => (
              <div key={index} style={{ 
                width: '80px', 
                height: '80px', 
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                flexShrink: 0,
                cursor: 'pointer'
              }}>
                <img 
                  src="/api/placeholder/80/80" 
                  alt={`${product.title} ${index + 1}`}
                  style={{ 
                    width: '100%', 
                    height: '100%', 
                    objectFit: 'cover',
                    borderRadius: '4px'
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Product Details */}
        <div>
          <h1 style={{ 
            fontSize: '24px', 
            fontWeight: 'bold', 
            marginBottom: '10px',
            lineHeight: '1.3'
          }}>
            {product.title}
          </h1>

          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '10px',
            marginBottom: '20px'
          }}>
            <span style={{ fontSize: '14px', color: '#666' }}>S • New • </span>
            <span style={{ color: '#007bff', fontSize: '14px' }}>H&M</span>
          </div>

          <div style={{ marginBottom: '20px' }}>
            <div style={{ fontSize: '14px', color: '#666', marginBottom: '5px' }}>
              ${product.price}.00
            </div>
            <div style={{ 
              fontSize: '28px', 
              fontWeight: 'bold', 
              color: '#333' 
            }}>
              ${product.price}.45
            </div>
            <div style={{ fontSize: '14px', color: '#666' }}>
              Includes Buyer Protection
            </div>
          </div>

          {/* Product Details Table */}
          <div style={{ marginBottom: '30px' }}>
            <table style={{ width: '100%', fontSize: '14px' }}>
              <tbody>
                <tr>
                  <td style={{ padding: '8px 0', color: '#666', width: '30%' }}>Brand</td>
                  <td style={{ padding: '8px 0', textAlign: 'right' }}>{product.brand}</td>
                </tr>
                <tr>
                  <td style={{ padding: '8px 0', color: '#666' }}>Size</td>
                  <td style={{ padding: '8px 0', textAlign: 'right' }}>{product.size}</td>
                </tr>
                <tr>
                  <td style={{ padding: '8px 0', color: '#666' }}>Condition</td>
                  <td style={{ padding: '8px 0', textAlign: 'right' }}>{product.condition}</td>
                </tr>
                <tr>
                  <td style={{ padding: '8px 0', color: '#666' }}>Color</td>
                  <td style={{ padding: '8px 0', textAlign: 'right' }}>{product.colors}</td>
                </tr>
                <tr>
                  <td style={{ padding: '8px 0', color: '#666' }}>Uploaded</td>
                  <td style={{ padding: '8px 0', textAlign: 'right' }}>12 days ago</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div style={{ marginBottom: '20px' }}>
            <p style={{ fontSize: '14px', lineHeight: '1.5', color: '#333' }}>
              {product.description}
            </p>
          </div>

          <div style={{ 
            fontSize: '14px', 
            color: '#666', 
            marginBottom: '30px' 
          }}>
            Shipping: ${product.shipping_cost}.18
          </div>

          {/* Action Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {!isOwnProduct ? (
              <>
                <button style={{
                  width: '100%',
                  padding: '16px',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}>
                  Buy now
                </button>

                <button style={{
                  width: '100%',
                  padding: '16px',
                  backgroundColor: 'white',
                  color: '#007bff',
                  border: '2px solid #007bff',
                  borderRadius: '6px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}>
                  Make an offer
                </button>

                {/* Chat Button Integration */}
                <ProductChatButton
                  productId={product.id}
                  currentUserId={currentUser.id}
                  buttonText="Message seller"
                  buttonStyle={{
                    backgroundColor: '#28a745',
                    padding: '16px'
                  }}
                />
              </>
            ) : (
              <div style={{ 
                padding: '16px', 
                backgroundColor: '#f8f9fa', 
                borderRadius: '6px',
                textAlign: 'center',
                color: '#666'
              }}>
                This is your product
              </div>
            )}
          </div>

          {/* Buyer Protection */}
          <div style={{ 
            marginTop: '30px',
            padding: '20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px'
          }}>
            <h3 style={{ 
              fontSize: '16px', 
              fontWeight: 'bold', 
              marginBottom: '10px' 
            }}>
              Buyer Protection for
            </h3>
            <p style={{ fontSize: '14px', color: '#666', margin: 0 }}>
              Get the item you ordered or get your money back.
            </p>
          </div>

          {/* Member's Items */}
          <div style={{ marginTop: '30px' }}>
            <h3 style={{ 
              fontSize: '18px', 
              fontWeight: 'bold', 
              marginBottom: '15px' 
            }}>
              Member's items (5)
            </h3>
            
            <div style={{ 
              display: 'flex', 
              gap: '15px',
              overflowX: 'auto'
            }}>
              {[1, 2, 3].map((item) => (
                <div key={item} style={{ 
                  width: '120px', 
                  flexShrink: 0 
                }}>
                  <div style={{ 
                    width: '120px', 
                    height: '120px', 
                    backgroundColor: '#f5f5f5',
                    borderRadius: '8px',
                    marginBottom: '8px'
                  }}>
                    <img 
                      src="/api/placeholder/120/120" 
                      alt={`Item ${item}`}
                      style={{ 
                        width: '100%', 
                        height: '100%', 
                        objectFit: 'cover',
                        borderRadius: '8px'
                      }}
                    />
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    ${8 + item}.00
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
