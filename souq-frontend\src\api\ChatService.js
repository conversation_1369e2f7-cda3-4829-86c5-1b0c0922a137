import apiService from './ApiService';

// Create or get chat for a product
export const createOrGetChat = (productId) =>
  apiService({
    url: `/chat/product/${productId}`,
    method: 'POST',
    withAuth: true,
  });

// Get all chats for the authenticated user
export const getUserChats = (page = 1, limit = 20) =>
  apiService({
    url: '/chat',
    method: 'GET',
    params: { page, limit },
    withAuth: true,
  });

// Get messages for a specific chat
export const getChatMessages = (chatId, page = 1, limit = 50) =>
  apiService({
    url: `/chat/${chatId}/messages`,
    method: 'GET',
    params: { page, limit },
    withAuth: true,
  });

// Send a message (HTTP fallback)
export const sendMessage = (chatId, messageData) =>
  apiService({
    url: `/api/user/chat/${chatId}/messages`,
    method: 'POST',
    data: messageData,
    withAuth: true,
  });

// Mark messages as seen
export const markMessagesAsSeen = (chatId) =>
  apiService({
    url: `/api/user/chat/${chatId}/seen`,
    method: 'PATCH',
    withAuth: true,
  });
