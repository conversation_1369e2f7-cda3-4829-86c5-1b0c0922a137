import React from "react";
import { useForm } from "react-hook-form";
import { useLocation, useNavigate } from "react-router-dom";
import { verifyEmail } from "../api/AuthService";

export default function EmailVerification() {
    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting, isValid },
        setValue,
        setError, 
        watch,
    } = useForm({
        mode: "onChange",
        defaultValues: {
            code: ""
        }
    });

    const navigate = useNavigate();
    const location = useLocation();
    const email = location?.state?.email;
    const code = watch("code");

    const onSubmit = async (data) => {
        try {
            const response = await verifyEmail({
                otp: data.code,
            });
            if (response.success) {
                console.log('✅ Email verified successfully');
                navigate("/send-phone-otp");
            } else {
                setError("code", {
                    type: "manual",
                    message: response.error || "Invalid verification code",
                });
            }
        } catch (err) {
            setError("code", {
                type: "manual",
                message: "Something went wrong. Please try again.",
            });
        }
    };

    const handleCodeChange = (e) => {
        const value = e.target.value.replace(/\D/g, "");
        if (value.length <= 6) {
            setValue("code", value, { shouldValidate: true });
        }
    };

    return (
        <div className="min-h-screen bg-gray-100 py-12 px-4">
            <div className="bg-white rounded-xl shadow-md p-8 max-w-md w-full mx-auto">
                <h1 className="text-2xl font-semibold mb-4 text-center">Email Verification</h1>
                <p className="text-center mb-6 text-gray-700">
                    Enter the verification code that we sent you to this email address:
                    <br />
                    <span className="font-medium">{email}</span>
                </p>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    <input
                        type="text"
                        inputMode="numeric"
                        autoComplete="one-time-code"
                        placeholder="Enter 6-digit code"
                        {...register("code", {
                            required: "Verification code is required",
                            minLength: {
                                value: 6,
                                message: "Code must be 6 digits"
                            },
                            maxLength: {
                                value: 6,
                                message: "Code must be 6 digits"
                            },
                            pattern: {
                                value: /^\d+$/,
                                message: "Only numbers are allowed"
                            }
                        })}
                        value={code}
                        onChange={handleCodeChange}
                        className={`w-full px-4 py-2 border rounded-md text-lg focus:outline-none ${errors.code ? "border-red-500" : "border-gray-300"
                            }`}
                    />
                    {errors.code && (
                        <p className="text-red-500 text-sm">{errors.code.message}</p>
                    )}

                    <button
                        type="submit"
                        disabled={!isValid || isSubmitting}
                        className="w-full bg-teal-600 hover:bg-teal-700 text-white py-2 rounded-md font-semibold disabled:opacity-60"
                    >
                        {isSubmitting ? "Verifying..." : "Verify"}
                    </button>
                </form>

                <div className="mt-4 text-center">
                    <button
                        type="button"
                        onClick={() =>
                            navigate("/email-not-receive", {
                                state: { email }
                            })
                        }
                        className="w-full hover:bg-gray-100 text-teal-700 py-2 rounded-md font-semibold border border-teal-600"
                    >
                        Didn’t receive our mail?
                    </button>
                </div>
            </div>
        </div>
    );
}
