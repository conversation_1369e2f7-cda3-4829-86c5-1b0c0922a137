import { useParams } from 'react-router-dom';
import usePageTitle from '../hooks/usePageTitle';

function ProductDetails() {
  const { id } = useParams();
  // Mock product name - replace with actual product data
  const productName = `Product ${id}`;
  usePageTitle(`${productName} | SOUQ E-commerce`);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="aspect-w-4 aspect-h-5 rounded-lg overflow-hidden">
          <img
            src={`https://picsum.photos/800/1000?random=${id}`}
            alt="Product"
            className="w-full h-full object-cover"
          />
        </div>
        
        <div className="space-y-6">
          <h1 className="text-2xl font-bold">Product {id}</h1>
          <p className="text-3xl font-bold text-green-600">$299</p>
          
          <div className="space-y-2">
            <p className="text-gray-600"><span className="font-medium">Brand:</span> Nike</p>
            <p className="text-gray-600"><span className="font-medium">Condition:</span> New</p>
            <p className="text-gray-600"><span className="font-medium">Size:</span> M</p>
          </div>
          
          <button className="w-full bg-green-500 text-white py-3 rounded-full hover:bg-green-600">
            Buy Now
          </button>
          
          <div className="border-t pt-6">
            <h2 className="text-lg font-medium mb-4">Description</h2>
            <p className="text-gray-600">
              This is a detailed description of the product. It includes information about the material,
              fit, and care instructions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProductDetails;