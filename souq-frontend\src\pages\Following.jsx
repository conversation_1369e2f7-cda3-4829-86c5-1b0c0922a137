// pages/FollowersPage.jsx
import React, { useEffect, useState } from "react";
import { FaClock, FaMapMarkerAlt, FaStar } from "react-icons/fa";
import { useNavigate, useParams } from "react-router-dom";
import { getProfileById } from "../api/AuthService";
import { getFollowing } from "../api/ProductService";
import FollowingCard from "../components/followers/FollowingCard";
import LoadingSpinner from "../components/common/LoadingSpinner";
import { formatDistanceToNowStrict } from "date-fns";

const FollowingPage = () => {
    const { id } = useParams();
    const navigate = useNavigate()
    const [profile, setProfile] = useState(null);
    const [followersData, setFollowersData] = useState([]);
    const baseURL = import.meta.env.VITE_API_BASE_URL;
    const normalizedURL = baseURL.endsWith('/') ? baseURL.slice(0, -1) : baseURL;
    const authUser = JSON.parse(localStorage.getItem("user"));

    useEffect(() => {
        getProfileById(id)
            .then((res) => {
                setProfile(res?.data?.data);
            })
            .catch((err) => {
                console.error("Failed to fetch profile", err);
            })
            .finally(() => {

            });
    }, [id]);

    useEffect(() => {
        getFollowing(id)
            .then((res) => {
                console.log(res?.data?.data?.following, "res")
                setFollowersData(res?.data?.data?.following)
            })
            .catch((err) => {
                console.error("Failed to fetch following", err);
            })
            .finally(() => {
            });
    }, [id]);

    if (!profile) {
        return <LoadingSpinner fullScreen />;
    }

    const userNavigate = () => {
        if (authUser?.id === profile?.id) {
            navigate("/member-profile")
        } else {
            navigate(`/profile/${profile?.id}`)
        }
    }

    return (
        <div className="min-h-screen bg-white p-6">
            <div className="container mx-auto">
                <div className="flex items-center gap-4 border-b pb-4 mb-6">
                    <img src={profile?.profile ? `${normalizedURL}${profile?.profile}` : "https://cdn-icons-png.flaticon.com/512/149/149071.png"} alt="danilelapink" className="w-16 h-16 rounded-full object-cover border border-gray-100 cursor-pointer" onClick={userNavigate} />
                    <div className="flex flex-col">
                        <span className="font-semibold text-gray-900">{profile?.firstName} {profile?.lastName}</span>
                        <div className="flex items-center text-yellow-500 text-sm mb-1">
                            {[...Array(5)].map((_, i) => (
                                <FaStar
                                    key={i}
                                    className={`text-lg text-yellow-500`}
                                />
                            ))}
                            <span className="text-gray-600 text-xs ml-1">115</span>
                        </div>
                        <div className="text-sm text-gray-500 mt-1 space-y-1">
                            <div className="flex items-center gap-2"><span><FaMapMarkerAlt className="text-sm" /></span>{profile?.city}, {profile?.country}</div>
                            <div className="flex items-center gap-2"><span> <FaClock className="text-sm" /></span>{profile?.lastLoginAt ? formatDistanceToNowStrict(new Date(profile?.lastLoginAt), { addSuffix: true }) : "1 Hours ago"}</div>
                        </div>
                    </div>
                </div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">{profile?.firstName} {profile?.lastName} following</h2>
                {followersData.length === 0 ? (
                    <div className="flex justify-center items-center h-60 w-full">
                        <p className="text-center text-gray-500">{profile?.firstName} {profile?.lastName} follow anyone yet</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4">
                        {followersData.map((person, index) => (
                            <FollowingCard key={index} person={person} />
                        ))}
                    </div>
                )}
            </div>
        </div>
    )
}

export default FollowingPage;