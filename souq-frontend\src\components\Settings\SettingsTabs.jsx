import React, { useEffect, useState } from "react";
import ProfileDetails from "./ProfileDetails";
import AccountSettings from "./AccountDetails";
import Address from "./Address";
import { getProfile } from "../../api/AuthService";
import PaymentOptions from "./PaymentOptions";
import BundleDiscounts from "./BundalDiscount";
import NotificationSettings from "./NotificationSettings";
import PrivacySettings from "./PrivacySetting";
import Security from "./Security";
import DeliverySettings from "./DeliverySettings";
import Wallet from "./Wallet";

const tabs = [
  "Profile details",
  "Account settings",
  "Shipping",
  "Delivery",
  "Payments",
  "Wallet",
  "Bundle discounts",
  "Notifications",
  "Privacy settings",
  "Security",
];

const SettingsTabs = () => {
  const [selectedTab, setSelectedTab] = useState(tabs[0]);
  const [profileData, setProfileData] = useState(""); // store user profile
  const [apiRefresh, seApiRefresh] = useState("")
  useEffect(() => {
    getProfile().then((res) => {
      if (res?.success) {
        setProfileData(res?.data?.data);
      }
    });
  }, [apiRefresh]);

  const renderTabContent = () => {
    switch (selectedTab) {
      case "Profile details":
        return <ProfileDetails profileData={profileData} apiRefresh={apiRefresh} seApiRefresh={seApiRefresh} />;
      case "Account settings":
        return <AccountSettings profileData={profileData} apiRefresh={apiRefresh} seApiRefresh={seApiRefresh} />;
      case "Shipping":
        return <Address />;
      case "Delivery":
        return <DeliverySettings />;
      case "Payments":
        return <PaymentOptions/>;
      case "Wallet":
        return <Wallet/>;
      case "Bundle discounts":
        return <BundleDiscounts/>;
      case "Notifications":
        return <NotificationSettings/>;
      case "Privacy settings":
        return <PrivacySettings/>;
      case "Security":
        return <Security/>;
      default:
        return <div className="p-4">Select a tab</div>;
    }
  };

  return (
    <div className="flex flex-col md:flex-row w-full">
      {/* Tabs sidebar */}
      <div className="w-full md:w-1/4 ltr:border-r rtl:border-l p-4 space-y-2">
        <h2 className="text-xl font-semibold mb-4">Settings</h2>
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => setSelectedTab(tab)}
            className={`w-full text-left rtl:text-right px-4 py-2 rounded-md transition-all duration-200 ${selectedTab === tab
              ? "bg-gray-100 font-semibold text-black"
              : "text-gray-600 hover:bg-gray-50"
              }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Tab content */}
      <div className="flex-1 p-4">{renderTabContent()}</div>
    </div>
  );
};

export default SettingsTabs;
