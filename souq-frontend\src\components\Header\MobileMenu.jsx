import React, { useState, useEffect } from 'react';
import { Search } from 'lucide-react';
import {
  LuBell, LuHeart, LuMail, LuUser, LuSettings, LuLogOut, LuWallet, LuPackage, LuGift, LuUsers, LuSparkles,
} from "react-icons/lu";
import { categories } from '../../data/categories';
import { useAppContext } from '../../context/AppContext';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { logout } from '../../api/AuthService';
import { clearTokens } from '../../utils/TokenStorage';

const MobileMenu = () => {
  const { setIsAuthModalOpen, setAuthMode, setIsMobileMenuOpen } = useAppContext();
  const profileImage = useSelector((state) => state.profile.profileImage);
  const [language, setLanguage] = useState('en');
  const navigate = useNavigate();
  const token = localStorage.getItem("user");

  useEffect(() => {
    const savedLang = localStorage.getItem("lang") || "en";
    setLanguage(savedLang);
    document.documentElement.lang = savedLang;
    document.documentElement.dir = savedLang === "ar" ? "rtl" : "ltr";
  }, []);

  const handleLogin = () => {
    setAuthMode('login');
    setIsAuthModalOpen(true);
    setIsMobileMenuOpen(false);
  };

  const handleSignup = () => {
    setAuthMode('signup');
    setIsAuthModalOpen(true);
    setIsMobileMenuOpen(false);
  };

  const handleSellNow = () => {
    if (token) {
      navigate("/sell-now");
    } else {
      handleLogin();
    }
    setIsMobileMenuOpen(false);
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      console.error(err);
    } finally {
      clearTokens();
      navigate('/');
      window.location.reload();
    }
  };

  const handleLanguageChange = (lang) => {
    setLanguage(lang);
    localStorage.setItem("lang", lang);
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === "ar" ? "rtl" : "ltr";
  };

  return (
    <div className="lg:hidden bg-white border-t border-gray-100 absolute left-0 right-0 z-50 shadow-lg">
      <div className="p-4 space-y-5">

        {/* 🔍 Search */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search products"
            className="pl-10 pr-4 py-2 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>

        {/* 👤 Auth/Profile */}
        {token ? (
          <>
            <div className="flex items-center gap-3">
              <img
                src={profileImage || "https://cdn-icons-png.flaticon.com/512/149/149071.png"}
                alt="Profile"
                className="w-10 h-10 rounded-full border"
              />
              <button onClick={() => navigate("/member-profile")} className="font-medium">
                My Profile
              </button>
            </div>

            {/* 🔔 Mail / Heart / Bell */}
            <div className="flex justify-around text-gray-600 pt-2">
              <LuMail onClick={() => navigate("/chat-layout")} className="cursor-pointer" />
              <LuBell onClick={() => navigate("/notifications")} className="cursor-pointer" />
              <LuHeart onClick={() => navigate("/favorites-item")} className="cursor-pointer" />
            </div>

            {/* 📦 Menu Options */}
            <ul className="pt-4 text-sm grid grid-cols-2 gap-3 text-gray-700">
              <li
                className="flex items-center gap-2 cursor-pointer"
                onClick={() => navigate("/settings")}
              >
                <LuSettings /> Settings
              </li>
              <li className="flex items-center gap-2 cursor-pointer">
                <LuSparkles /> Personalization
              </li>
              <li className="flex items-center gap-2 cursor-pointer" onClick={() => navigate("/wallet")}>
                <LuWallet /> Wallet
              </li>
              <li className="flex items-center gap-2 cursor-pointer">
                <LuPackage /> My Orders
              </li>
              <li className="flex items-center gap-2 cursor-pointer">
                <LuGift /> Donation
              </li>
              <li className="flex items-center gap-2 cursor-pointer">
                <LuUsers /> Invite Friends
              </li>
              <li
                className="flex items-center gap-2 cursor-pointer text-red-500 col-span-2"
                onClick={handleLogout}
              >
                <LuLogOut /> Logout
              </li>
            </ul>

          </>
        ) : (
          <div className="border border-teal-500 flex items-center rounded-full overflow-hidden">
            <button onClick={handleSignup} className="text-teal-500 flex-1 py-2 text-center hover:bg-teal-50">Sign up</button>
            <div className="h-6 w-px bg-teal-500" />
            <button onClick={handleLogin} className="text-teal-500 flex-1 py-2 text-center hover:bg-teal-50">Log in</button>
          </div>
        )}

        {/* 🛍️ Sell Now + 🌐 Language Switcher */}
        <div className="flex items-center justify-between gap-4">
          <button
            onClick={handleSellNow}
            className="flex-1 bg-teal-600 hover:bg-teal-700 text-white py-2 rounded-full font-semibold"
          >
            Sell Now
          </button>
          <div className="flex items-center gap-2">
            <button
              onClick={() => handleLanguageChange("en")}
              className={`px-3 py-1 rounded-full border text-sm ${language === "en" ? "bg-teal-600 text-white" : "text-gray-600"}`}
            >
              EN
            </button>
            <button
              onClick={() => handleLanguageChange("ar")}
              className={`px-3 py-1 rounded-full border text-sm ${language === "ar" ? "bg-teal-600 text-white" : "text-gray-600"}`}
            >
              AR
            </button>
          </div>
        </div>

        {/* 📂 Categories */}
        <div>
          <h3 className="font-semibold text-gray-800">Categories</h3>
          <ul className="grid grid-cols-2 gap-3 pt-2">
            {categories.map((category) => (
              <li key={category.id}>
                <button
                  onClick={() => {
                    // navigate(`/category/${category.slug}`);
                    setIsMobileMenuOpen(false);
                  }}
                  className="text-gray-700 hover:text-teal-600 font-medium text-left"
                >
                  {category.name}
                </button>
              </li>
            ))}
          </ul>
        </div>

      </div>
    </div>
  );
};

export default MobileMenu;
