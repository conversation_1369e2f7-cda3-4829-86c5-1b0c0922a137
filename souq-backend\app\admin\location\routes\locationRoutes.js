const express = require('express');
const router = express.Router();
const countryController = require('../controllers/countryController');
const cityController = require('../controllers/cityController');
const { verifyAdminToken, checkPermission } = require('../../auth/middleware/adminAuthMiddleware');

// All routes require admin authentication
router.use(verifyAdminToken);

// Country routes
router.get('/countries', checkPermission('locations', 'view'), countryController.getAllCountries);
router.get('/countries/stats', checkPermission('locations', 'view'), countryController.getCountryStats);
router.get('/countries/:id', checkPermission('locations', 'view'), countryController.getCountryById);
router.post('/countries', checkPermission('locations', 'create'), countryController.createCountry);
router.put('/countries/:id', checkPermission('locations', 'edit'), countryController.updateCountry);
router.delete('/countries/:id', checkPermission('locations', 'delete'), countryController.deleteCountry);

// City routes
router.get('/cities', checkPermission('locations', 'view'), cityController.getAllCities);
router.get('/cities/stats', checkPermission('locations', 'view'), cityController.getCityStats);
router.get('/cities/:id', checkPermission('locations', 'view'), cityController.getCityById);
router.post('/cities', checkPermission('locations', 'create'), cityController.createCity);
router.put('/cities/:id', checkPermission('locations', 'edit'), cityController.updateCity);
router.delete('/cities/:id', checkPermission('locations', 'delete'), cityController.deleteCity);

module.exports = router;
