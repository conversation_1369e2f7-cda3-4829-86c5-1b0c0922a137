import React, { useState, useEffect } from 'react';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { adminApi } from '../../services/adminApi';
import usePageTitle from '../../hooks/usePageTitle';

const Analytics = () => {
  usePageTitle('Admin | Analytics');
  
  const { hasPermission } = useAdminAuth();
  const [salesAnalytics, setSalesAnalytics] = useState(null);
  const [topSellers, setTopSellers] = useState([]);
  const [categoryTrends, setCategoryTrends] = useState([]);
  const [userAnalytics, setUserAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [period, setPeriod] = useState('30d');

  useEffect(() => {
    fetchAnalytics();
  }, [period]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const [salesResponse, sellersResponse, trendsResponse, usersResponse] = await Promise.all([
        adminApi.getSalesAnalytics({ period }),
        adminApi.getTopSellers({ period }),
        adminApi.getCategoryTrends({ period }),
        adminApi.getUserAnalytics()
      ]);

      if (salesResponse.success) setSalesAnalytics(salesResponse.data.analytics);
      if (sellersResponse.success) setTopSellers(sellersResponse.data.topSellers);
      if (trendsResponse.success) setCategoryTrends(trendsResponse.data.categoryTrends);
      if (usersResponse.success) setUserAnalytics(usersResponse.data.analytics);
    } catch (error) {
      console.error('Analytics fetch error:', error);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (!hasPermission('analytics', 'view')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          You don't have permission to view analytics.
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600">Comprehensive business insights and metrics</p>
          </div>
          <div>
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="365d">Last year</option>
            </select>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Sales Overview */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Sales Overview</h3>
          {salesAnalytics?.salesOverTime && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {salesAnalytics.salesOverTime.reduce((sum, item) => sum + item.orders, 0)}
                  </div>
                  <div className="text-sm text-blue-600">Total Orders</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(salesAnalytics.salesOverTime.reduce((sum, item) => sum + item.sales, 0))}
                  </div>
                  <div className="text-sm text-green-600">Total Sales</div>
                </div>
              </div>
              
              {/* Simple sales chart representation */}
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Sales Trend</h4>
                <div className="space-y-2">
                  {salesAnalytics.salesOverTime.slice(-7).map((item, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-16 text-xs text-gray-500">
                        {item._id.month}/{item._id.day}
                      </div>
                      <div className="flex-1 bg-gray-200 rounded-full h-2 ml-2">
                        <div 
                          className="bg-teal-600 h-2 rounded-full" 
                          style={{ 
                            width: `${Math.min((item.sales / Math.max(...salesAnalytics.salesOverTime.map(s => s.sales))) * 100, 100)}%` 
                          }}
                        ></div>
                      </div>
                      <div className="w-20 text-xs text-gray-600 ml-2">
                        {formatCurrency(item.sales)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Payment Methods */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Methods</h3>
          {salesAnalytics?.paymentMethods && (
            <div className="space-y-3">
              {salesAnalytics.paymentMethods.map((method, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-teal-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-gray-700 capitalize">
                      {method._id || 'Unknown'}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">{method.count}</div>
                    <div className="text-xs text-gray-500">{formatCurrency(method.amount)}</div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Top Sellers */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Sellers</h3>
          <div className="space-y-4">
            {topSellers.slice(0, 5).map((seller, index) => (
              <div key={seller._id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                    <span className="text-sm font-medium text-gray-700">
                      {index + 1}
                    </span>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{seller.sellerName}</div>
                    <div className="text-xs text-gray-500">{seller.sellerEmail}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrency(seller.totalSales)}
                  </div>
                  <div className="text-xs text-gray-500">{seller.totalOrders} orders</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Category Trends */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Category Performance</h3>
          <div className="space-y-4">
            {categoryTrends.slice(0, 5).map((category, index) => (
              <div key={category._id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">{category.categoryName}</span>
                  <div className="text-right">
                    <span className="text-sm font-medium text-gray-900">
                      {formatCurrency(category.sales)}
                    </span>
                    {category.growth && (
                      <span className={`ml-2 text-xs ${
                        category.growth.sales >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {category.growth.sales >= 0 ? '+' : ''}{category.growth.sales.toFixed(1)}%
                      </span>
                    )}
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-teal-600 h-2 rounded-full" 
                    style={{ 
                      width: `${Math.min((category.sales / Math.max(...categoryTrends.map(c => c.sales))) * 100, 100)}%` 
                    }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500">{category.orders} orders</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* User Analytics */}
      {userAnalytics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">User Activity</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {userAnalytics.userActivity?.totalUsers || 0}
                </div>
                <div className="text-sm text-blue-600">Total Users</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {userAnalytics.userActivity?.activeUsers || 0}
                </div>
                <div className="text-sm text-green-600">Active Users</div>
              </div>
            </div>
            <div className="mt-4">
              <div className="text-sm text-gray-600">
                Average orders per user: {userAnalytics.userActivity?.avgOrdersPerUser?.toFixed(1) || 0}
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-4">User Retention</h3>
            <div className="space-y-3">
              {userAnalytics.userRetention?.map((retention, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{retention._id}</span>
                  <span className="text-sm font-medium text-gray-900">{retention.count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Analytics;
