// src/pages/OauthCallback.js
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const AuthCallback = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // This assumes the backend redirects like:
    // http://localhost:3000/oauth/callback?accessToken=...&refreshToken=...&user=...

    const urlParams = new URLSearchParams(window.location.search);
    const accessToken = urlParams.get('accessToken');
    const refreshToken = urlParams.get('refreshToken');
    const user = JSON.parse(decodeURIComponent(urlParams.get('user')));

    if (accessToken && user) {
      // Save tokens & user data in localStorage or your context
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
      localStorage.setItem('user', JSON.stringify(user));

      // Navigate to dashboard or homepage
      navigate('/');
    } else {
      console.error("Invalid login redirect");
      navigate('/');
    }
  }, [navigate]);

  return <p>Logging you in...</p>;
};

export default AuthCallback;
