import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  TrendingUp, 
  DollarSign, 
  Users, 
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Filter,
  Download
} from 'lucide-react';

const EscrowDashboard = () => {
  const [stats, setStats] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('30d');
  const [filters, setFilters] = useState({
    status: '',
    gateway: '',
    currency: ''
  });

  useEffect(() => {
    loadDashboardData();
  }, [period]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Mock API calls - replace with actual API calls
      const mockStats = {
        overview: {
          totalTransactions: 1247,
          totalVolume: 456789.50,
          totalPlatformFees: 45678.95,
          completedTransactions: 1156,
          pendingTransactions: 67,
          failedTransactions: 18,
          disputedTransactions: 6,
          successRate: 92.7,
          averageTransactionValue: 366.45
        },
        breakdowns: {
          status: [
            { _id: 'completed', count: 1156, totalAmount: 423456.78 },
            { _id: 'pending_payment', count: 34, totalAmount: 12456.89 },
            { _id: 'funds_held', count: 23, totalAmount: 8934.56 },
            { _id: 'shipped', count: 10, totalAmount: 3678.90 },
            { _id: 'payment_failed', count: 18, totalAmount: 6789.12 },
            { _id: 'disputed', count: 6, totalAmount: 1473.25 }
          ],
          gateway: [
            { _id: 'paytabs', count: 567, totalAmount: 208934.56, successRate: 0.94 },
            { _id: 'stripe', count: 423, totalAmount: 156789.34, successRate: 0.91 },
            { _id: 'paypal', count: 257, totalAmount: 91065.60, successRate: 0.89 }
          ],
          currency: [
            { _id: 'AED', count: 892, totalAmount: 326789.45 },
            { _id: 'USD', count: 234, totalAmount: 87654.32 },
            { _id: 'EUR', count: 89, totalAmount: 32456.78 },
            { _id: 'GBP', count: 32, totalAmount: 9888.95 }
          ]
        }
      };

      const mockTransactions = [
        {
          _id: '1',
          transactionId: 'ESC-1703123456-ABC123',
          status: 'completed',
          totalAmount: 299.99,
          currency: 'AED',
          paymentGateway: 'paytabs',
          buyer: { firstName: 'Ahmed', lastName: 'Ali', email: '<EMAIL>' },
          seller: { firstName: 'Sara', lastName: 'Hassan', email: '<EMAIL>' },
          product: { title: 'Designer Handbag', price: 299.99 },
          createdAt: new Date().toISOString()
        },
        {
          _id: '2',
          transactionId: 'ESC-1703123457-DEF456',
          status: 'funds_held',
          totalAmount: 150.00,
          currency: 'USD',
          paymentGateway: 'stripe',
          buyer: { firstName: 'John', lastName: 'Smith', email: '<EMAIL>' },
          seller: { firstName: 'Maria', lastName: 'Garcia', email: '<EMAIL>' },
          product: { title: 'Vintage Watch', price: 150.00 },
          createdAt: new Date(Date.now() - 86400000).toISOString()
        }
      ];

      setStats(mockStats);
      setTransactions(mockTransactions);
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      completed: 'text-green-600 bg-green-100',
      pending_payment: 'text-yellow-600 bg-yellow-100',
      payment_processing: 'text-blue-600 bg-blue-100',
      funds_held: 'text-teal-600 bg-teal-100',
      shipped: 'text-purple-600 bg-purple-100',
      delivered: 'text-green-600 bg-green-100',
      payment_failed: 'text-red-600 bg-red-100',
      disputed: 'text-orange-600 bg-orange-100',
      cancelled: 'text-gray-600 bg-gray-100'
    };
    return colors[status] || 'text-gray-600 bg-gray-100';
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Escrow Management</h1>
          <p className="text-gray-600">Monitor and manage escrow transactions</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <button
            onClick={loadDashboardData}
            className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Transactions</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.overview.totalTransactions.toLocaleString()}
              </p>
            </div>
            <Shield className="w-8 h-8 text-teal-600" />
          </div>
          <div className="mt-2 text-sm text-green-600">
            Success Rate: {stats?.overview.successRate}%
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Volume</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(stats?.overview.totalVolume)}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-600" />
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Avg: {formatCurrency(stats?.overview.averageTransactionValue)}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Platform Fees</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(stats?.overview.totalPlatformFees)}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-blue-600" />
          </div>
          <div className="mt-2 text-sm text-gray-600">
            10% of transaction volume
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Actions</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.overview.pendingTransactions + stats?.overview.disputedTransactions}
              </p>
            </div>
            <AlertTriangle className="w-8 h-8 text-orange-600" />
          </div>
          <div className="mt-2 text-sm text-orange-600">
            {stats?.overview.disputedTransactions} disputes
          </div>
        </div>
      </div>

      {/* Charts and Breakdowns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Breakdown */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Transaction Status</h3>
          <div className="space-y-3">
            {stats?.breakdowns.status.map((item) => (
              <div key={item._id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item._id)}`}>
                    {item._id.replace('_', ' ').toUpperCase()}
                  </span>
                  <span className="text-sm text-gray-600">{item.count} transactions</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {formatCurrency(item.totalAmount)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Gateway Performance */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Gateway Performance</h3>
          <div className="space-y-3">
            {stats?.breakdowns.gateway.map((item) => (
              <div key={item._id} className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900 capitalize">{item._id}</div>
                  <div className="text-sm text-gray-600">
                    {item.count} transactions • {(item.successRate * 100).toFixed(1)}% success
                  </div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {formatCurrency(item.totalAmount)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
            <div className="flex items-center space-x-2">
              <button className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </button>
              <button className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50">
                <Download className="w-4 h-4 mr-2" />
                Export
              </button>
            </div>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Transaction
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Parties
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Gateway
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {transactions.map((transaction) => (
                <tr key={transaction._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.transactionId}
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.product.title}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div>Buyer: {transaction.buyer.firstName} {transaction.buyer.lastName}</div>
                      <div>Seller: {transaction.seller.firstName} {transaction.seller.lastName}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(transaction.totalAmount, transaction.currency)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                      {transaction.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                    {transaction.paymentGateway}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(transaction.createdAt).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default EscrowDashboard;
