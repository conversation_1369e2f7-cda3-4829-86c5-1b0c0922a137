import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';

const EmailChange = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [email, setEmail] = useState('');

    useEffect(() => {
        if (location?.state?.email) {
            setEmail(location.state.email);
        }
    }, [location]);

    const {
        handleSubmit,
        formState: { isSubmitting },
    } = useForm();

    const onSubmit = async () => {
        // try {
        //     if (!email) return alert("Please enter an email address");

        //     // Simulate API call delay
        //     await new Promise((resolve) => setTimeout(resolve, 2000));
        //     console.log('Verification email resent to:', email);

        //     navigate('/email-verify', { state: { email } });
        // } catch (error) {
        //     console.error('Resend failed:', error);
        // }
    };

    return (
        <div className="min-h-screen bg-gray-100 py-12 px-4">
            <div className="bg-white rounded-xl shadow-md p-8 max-w-md w-full mx-auto">
                <h1 className="text-2xl font-semibold mb-4 text-center">Confirm change</h1>
                <p className="text-center text-gray-600 mb-6 leading-relaxed">
                    You need to confirm <span className="text-teal-600 font-medium">{email}</span> is your email address before you can update it.
                </p>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    <button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-teal-600 hover:bg-teal-700 text-white py-2 rounded-md font-semibold disabled:opacity-60 flex items-center justify-center gap-2"
                    >
                        {isSubmitting && (
                            <AiOutlineLoading3Quarters className="animate-spin h-5 w-5" />
                        )}
                        Send confirmation email
                    </button>

                    <button
                        type="button"
                        className="w-full hover:bg-gray-100 text-teal-700 py-2 rounded-md font-semibold border border-teal-600"
                    >
                        I dont have access to this email
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EmailChange;
