import axios from 'axios';
import { getAccessToken } from '../utils/TokenStorage';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Create axios instance with base configuration
const api = axios.create({
  baseURL: `${API_BASE_URL}/api/user/offer`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Offer API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Create a new offer
export const createOffer = async (chatId, offerData) => {
  try {
    const response = await api.post(`/chat/${chatId}`, offerData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Get offer details
export const getOffer = async (offerId) => {
  try {
    const response = await api.get(`/${offerId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Accept offer (seller only)
export const acceptOffer = async (offerId, message = '') => {
  try {
    const response = await api.patch(`/${offerId}/accept`, { message });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Decline offer (seller only)
export const declineOffer = async (offerId, message = '') => {
  try {
    const response = await api.patch(`/${offerId}/decline`, { message });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Get active offer for a chat
export const getChatOffer = async (chatId) => {
  try {
    const response = await api.get(`/chat/${chatId}/active`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export default {
  createOffer,
  getOffer,
  acceptOffer,
  declineOffer,
  getChatOffer
};
