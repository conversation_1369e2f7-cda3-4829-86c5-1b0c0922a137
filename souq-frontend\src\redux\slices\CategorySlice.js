import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  selectedCategory: null,
  selectedSubcategory: null,
  selectedItem: null,
};

const categorySlice = createSlice({
  name: "category",
  initialState,
  reducers: {
    setCategory: (state, action) => {
      state.selectedCategory = action.payload;
    },
    setSubcategory: (state, action) => {
      state.selectedSubcategory = action.payload;
    },
    setItem: (state, action) => {
      state.selectedItem = action.payload;
    },
    resetCategory: (state) => {
      state.selectedCategory = null;
      state.selectedSubcategory = null;
      state.selectedItem = null;
    },
  },
});

export const { setCategory, setSubcategory, setItem, resetCategory } = categorySlice.actions;

export default categorySlice.reducer;
