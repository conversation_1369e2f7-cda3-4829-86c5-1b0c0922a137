const Rating = require('../../../../db/models/ratingModel');
const Transaction = require('../../../../db/models/transactionModel');
const EscrowTransaction = require('../../../../db/models/escrowTransactionModel');
const User = require('../../../../db/models/userModel');
const Product = require('../../../../db/models/productModel');

/**
 * Submit a rating for a transaction
 */
exports.submitRating = async (req, res) => {
  try {
    const userId = req.user.id;
    const { transactionId } = req.params;
    const {
      rating,
      review,
      categories,
      ratingType
    } = req.body;

    // Validate required fields
    if (!rating || !ratingType) {
      return res.status(400).json({
        success: false,
        error: 'Rating and rating type are required'
      });
    }

    // Validate rating value
    if (!Number.isInteger(rating) || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        error: 'Rating must be an integer between 1 and 5'
      });
    }

    // Find the escrow transaction using utility function
    const { findEscrowTransaction } = require('../../../../utils/transactionUtils');
    const escrowTransaction = await findEscrowTransaction(transactionId, true);

    if (!escrowTransaction) {
      console.log(`❌ Transaction not found: ${transactionId}`);
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    console.log(`✅ Found transaction: ${escrowTransaction.transactionId} (status: ${escrowTransaction.status})`);

    // Verify user is part of this transaction using ObjectIds
    const userObjectId = req.user._id; // MongoDB ObjectId
    const isBuyer = escrowTransaction.buyer._id.toString() === userObjectId.toString();
    const isSeller = escrowTransaction.seller._id.toString() === userObjectId.toString();

    if (!isBuyer && !isSeller) {
      return res.status(403).json({
        success: false,
        error: 'You are not authorized to rate this transaction'
      });
    }

    // Validate rating type matches user role
    if ((isBuyer && ratingType !== 'buyer_to_seller') || 
        (isSeller && ratingType !== 'seller_to_buyer')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid rating type for your role in this transaction'
      });
    }

    // Check if transaction is completed or funds are held (payment successful)
    if (escrowTransaction.status !== 'completed' && escrowTransaction.status !== 'funds_held') {
      return res.status(400).json({
        success: false,
        error: 'You can only rate transactions where payment has been processed'
      });
    }

    // Determine who is being rated
    const ratedUserId = isBuyer ? escrowTransaction.seller._id : escrowTransaction.buyer._id;

    // Check if rating already exists using ObjectIds
    const existingRating = await Rating.findOne({
      escrowTransaction: escrowTransaction._id,
      ratedBy: userObjectId,
      ratingType: ratingType
    });

    if (existingRating) {
      return res.status(400).json({
        success: false,
        error: 'You have already rated this transaction'
      });
    }

    // Create the rating using ObjectIds
    const newRating = new Rating({
      escrowTransaction: escrowTransaction._id,
      product: escrowTransaction.product._id,
      ratedBy: userObjectId,
      ratedUser: ratedUserId,
      ratingType: ratingType,
      rating: rating,
      review: review || '',
      categories: categories || {},
      status: 'published',
      metadata: {
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      }
    });

    await newRating.save();

    // Populate the rating for response
    await newRating.populate([
      { path: 'ratedBy', select: 'firstName lastName profile' },
      { path: 'ratedUser', select: 'firstName lastName profile' },
      { path: 'product', select: 'title product_photos' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Rating submitted successfully',
      data: {
        rating: newRating
      }
    });

  } catch (error) {
    console.error('Error submitting rating:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to submit rating'
    });
  }
};

/**
 * Get ratings for a specific user
 */
exports.getUserRatings = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 10, type = 'received' } = req.query;

    const query = type === 'received' 
      ? { ratedUser: userId, status: 'published' }
      : { ratedBy: userId, status: 'published' };

    const ratings = await Rating.find(query)
      .populate('ratedBy', 'firstName lastName profile')
      .populate('ratedUser', 'firstName lastName profile')
      .populate('product', 'title product_photos')
      .populate('escrowTransaction', 'transactionId createdAt')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const totalRatings = await Rating.countDocuments(query);

    // Get user's average rating
    const averageRating = await Rating.getUserAverageRating(userId);

    res.json({
      success: true,
      data: {
        ratings,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalRatings / limit),
          totalRatings,
          hasNext: page * limit < totalRatings,
          hasPrev: page > 1
        },
        averageRating
      }
    });

  } catch (error) {
    console.error('Error fetching user ratings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user ratings'
    });
  }
};

/**
 * Get ratings for a specific product
 */
exports.getProductRatings = async (req, res) => {
  try {
    const { productId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const ratings = await Rating.find({
      product: productId,
      status: 'published'
    })
      .populate('ratedBy', 'firstName lastName profile')
      .populate('ratedUser', 'firstName lastName profile')
      .populate('escrowTransaction', 'transactionId createdAt')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const totalRatings = await Rating.countDocuments({
      product: productId,
      status: 'published'
    });

    // Get product's average rating
    const averageRating = await Rating.getProductAverageRating(productId);

    res.json({
      success: true,
      data: {
        ratings,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalRatings / limit),
          totalRatings,
          hasNext: page * limit < totalRatings,
          hasPrev: page > 1
        },
        averageRating
      }
    });

  } catch (error) {
    console.error('Error fetching product ratings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch product ratings'
    });
  }
};

/**
 * Get pending ratings for a user (ratings they need to submit)
 */
exports.getPendingRatings = async (req, res) => {
  try {
    const userId = req.user.id; // This is the UUID from the user
    const userObjectId = req.user._id; // This is the MongoDB ObjectId

    console.log(`🌟 Getting pending ratings for user: ${userId} (ObjectId: ${userObjectId})`);

    // Find completed or funds_held transactions where user hasn't rated yet
    // Use the MongoDB ObjectId for querying EscrowTransaction
    const completedTransactions = await EscrowTransaction.find({
      $or: [
        { buyer: userObjectId },
        { seller: userObjectId }
      ],
      status: { $in: ['completed', 'funds_held'] }
    })
      .populate('buyer', 'firstName lastName profile')
      .populate('seller', 'firstName lastName profile')
      .populate('product', 'title product_photos')
      .sort({ updatedAt: -1 });

    console.log(`📦 Found ${completedTransactions.length} completed transactions`);

    const pendingRatings = [];

    for (const transaction of completedTransactions) {
      const isBuyer = transaction.buyer._id.toString() === userObjectId.toString();
      const ratingType = isBuyer ? 'buyer_to_seller' : 'seller_to_buyer';

      console.log(`🔍 Checking transaction ${transaction.transactionId}: isBuyer=${isBuyer}, ratingType=${ratingType}`);

      // Check if user has already rated this transaction
      // Use the MongoDB ObjectId for querying Rating
      const existingRating = await Rating.findOne({
        escrowTransaction: transaction._id,
        ratedBy: userObjectId,
        ratingType: ratingType
      });

      if (!existingRating) {
        console.log(`✅ Pending rating found for transaction: ${transaction.transactionId}`);
        pendingRatings.push({
          transaction,
          ratingType,
          ratedUser: isBuyer ? transaction.seller : transaction.buyer
        });
      } else {
        console.log(`⚠️ Rating already exists for transaction: ${transaction.transactionId}`);
      }
    }

    console.log(`🌟 Total pending ratings: ${pendingRatings.length}`);

    res.json({
      success: true,
      data: {
        ratings: pendingRatings, // Changed from pendingRatings to ratings for consistency
        totalPending: pendingRatings.length
      }
    });

  } catch (error) {
    console.error('❌ Error fetching pending ratings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch pending ratings'
    });
  }
};

/**
 * Check if user can rate a specific transaction
 */
exports.canRateTransaction = async (req, res) => {
  try {
    const userId = req.user.id; // UUID
    const userObjectId = req.user._id; // MongoDB ObjectId
    const { transactionId } = req.params;

    console.log(`🌟 Checking if user ${userId} can rate transaction: ${transactionId}`);

    // Use the transaction utility to safely find the transaction
    const { findEscrowTransaction, findStandardPayment } = require('../../../../utils/transactionUtils');

    // Try to find as escrow first, then standard
    let transaction = await findEscrowTransaction(transactionId, true);
    let transactionType = 'escrow';

    if (!transaction) {
      transaction = await findStandardPayment(transactionId, true);
      transactionType = 'standard';
    }

    if (!transaction) {
      console.log(`❌ Transaction not found: ${transactionId}`);
      return res.status(404).json({
        success: false,
        error: 'Transaction not found'
      });
    }

    console.log(`✅ Found ${transactionType} transaction: ${transaction.transactionId} (status: ${transaction.status})`);

    // Compare using MongoDB ObjectIds, not UUIDs
    const isBuyer = transaction.buyer._id.toString() === userObjectId.toString();
    const isSeller = transaction.seller._id.toString() === userObjectId.toString();

    console.log(`👤 User role check: isBuyer=${isBuyer}, isSeller=${isSeller}`);

    if (!isBuyer && !isSeller) {
      console.log(`❌ User not authorized for transaction: ${transactionId}`);
      return res.json({
        success: true,
        data: { canRate: false, reason: 'Not part of transaction' }
      });
    }

    // Check if transaction is ready for rating
    const validStatuses = ['completed', 'funds_held', 'processing', 'pending_payment'];
    if (!validStatuses.includes(transaction.status)) {
      console.log(`⚠️ Transaction not ready for rating. Status: ${transaction.status}`);
      return res.json({
        success: true,
        data: { canRate: false, reason: 'Transaction payment not processed' }
      });
    }

    const ratingType = isBuyer ? 'buyer_to_seller' : 'seller_to_buyer';

    // Check for existing rating based on transaction type
    const ratingQuery = {
      ratedBy: userObjectId,
      ratingType: ratingType
    };

    if (transactionType === 'escrow') {
      ratingQuery.escrowTransaction = transaction._id;
    } else {
      ratingQuery.standardPayment = transaction._id;
    }

    const existingRating = await Rating.findOne(ratingQuery);

    if (existingRating) {
      console.log(`⚠️ Rating already exists for transaction: ${transactionId}`);
      return res.json({
        success: true,
        data: { canRate: false, reason: 'Already rated' }
      });
    }

    console.log(`✅ User can rate transaction: ${transactionId} (type: ${ratingType})`);

    res.json({
      success: true,
      data: {
        canRate: true,
        ratingType,
        userRole: isBuyer ? 'buyer' : 'seller',
        transactionType
      }
    });

  } catch (error) {
    console.error('❌ Error checking rating eligibility:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check rating eligibility'
    });
  }
};

/**
 * Test endpoint to debug user ID issues
 */
exports.debugUserIds = async (req, res) => {
  try {
    const userId = req.user.id; // UUID
    const userObjectId = req.user._id; // MongoDB ObjectId

    console.log(`🧪 Debug User IDs:`);
    console.log(`  - UUID (req.user.id): ${userId}`);
    console.log(`  - ObjectId (req.user._id): ${userObjectId}`);
    console.log(`  - User object:`, req.user);

    res.json({
      success: true,
      data: {
        userId: userId,
        userObjectId: userObjectId.toString(),
        userType: typeof userId,
        objectIdType: typeof userObjectId,
        isValidObjectId: userObjectId.toString().match(/^[0-9a-fA-F]{24}$/) ? true : false
      }
    });

  } catch (error) {
    console.error('❌ Error in debug endpoint:', error);
    res.status(500).json({
      success: false,
      error: 'Debug failed'
    });
  }
};
