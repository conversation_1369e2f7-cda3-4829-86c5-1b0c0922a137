import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAdminAuth } from '../../../context/AdminAuthContext';
import { combineClasses } from '../../../utils/responsive';

const AdminNav = () => {
    const location = useLocation();
    const { admin, logout, hasPermission } = useAdminAuth();
    const [showUserMenu, setShowUserMenu] = useState(false);
    const [showMobileMenu, setShowMobileMenu] = useState(false);

    const isActive = (path) => {
        return location.pathname === path || location.pathname.startsWith(path + '/');
    };

    const handleLogout = async () => {
        await logout();
        setShowUserMenu(false);
    };

    const navItems = [
        { path: '/admin', label: 'Dashboard', show: true },
        { path: '/admin/users', label: 'Users', show: hasPermission('users', 'view') },
        { path: '/admin/listings', label: 'Listings', show: hasPermission('listings', 'view') },
        // { path: '/admin/disputes', label: 'Disputes', show: hasPermission('disputes', 'view') },
        // { path: '/admin/counterfeit', label: 'Counterfeit', show: hasPermission('counterfeit', 'view') },
        { path: '/admin/analytics', label: 'Analytics', show: hasPermission('analytics', 'view') },
        { path: '/admin/categories', label: 'Categories', show: true },
        { path: '/admin/products', label: 'Products', show: true },
        { path: '/admin/menus', label: 'Menus', show: true },
        { path: '/admin/sizes', label: 'Sizes', show: hasPermission('sizes', 'view') }
    ];

    return (
        <nav className="bg-white shadow-sm sticky top-0 z-40">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16">
                    <div className="flex items-center">
                        <div className="flex-shrink-0 flex items-center">
                            <Link to="/admin" className="text-lg sm:text-xl font-bold text-teal-600">
                                <span className="hidden sm:inline">SOUQ Admin</span>
                                <span className="sm:hidden">SOUQ</span>
                            </Link>
                        </div>

                        {/* Desktop Navigation */}
                        <div className="hidden lg:ml-6 lg:flex lg:space-x-4 xl:space-x-8">
                            {navItems.filter(item => item.show).map((item) => (
                                <Link
                                    key={item.path}
                                    to={item.path}
                                    className={combineClasses(
                                        'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
                                        isActive(item.path)
                                            ? 'border-teal-500 text-gray-900'
                                            : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                                    )}
                                >
                                    {item.label}
                                </Link>
                            ))}
                        </div>
                    </div>
                    {/* Right side - User Menu and Mobile Menu Button */}
                    <div className="flex items-center space-x-2 sm:space-x-4">
                        {/* Mobile menu button */}
                        <button
                            onClick={() => setShowMobileMenu(!showMobileMenu)}
                            className="lg:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-teal-500"
                        >
                            <span className="sr-only">Open main menu</span>
                            {showMobileMenu ? (
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            ) : (
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            )}
                        </button>

                        {/* User Menu */}
                        <div className="relative">
                            <button
                                onClick={() => setShowUserMenu(!showUserMenu)}
                                className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                            >
                                <div className="h-8 w-8 sm:h-9 sm:w-9 rounded-full bg-teal-600 flex items-center justify-center">
                                    <span className="text-sm font-medium text-white">
                                        {admin?.firstName?.[0]}{admin?.lastName?.[0]}
                                    </span>
                                </div>
                                <span className="ml-2 text-gray-700 hidden md:block text-sm lg:text-base">
                                    {admin?.firstName} {admin?.lastName}
                                </span>
                                <svg className="ml-1 h-4 w-4 text-gray-400 hidden md:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>

                            {showUserMenu && (
                                <div className="absolute right-0 mt-2 w-48 sm:w-56 bg-white rounded-md shadow-lg py-1 z-50 border">
                                    <div className="px-4 py-3 text-sm text-gray-700 border-b">
                                        <div className="font-medium truncate">{admin?.firstName} {admin?.lastName}</div>
                                        <div className="text-gray-500 truncate text-xs sm:text-sm">{admin?.email}</div>
                                        <div className="text-xs text-gray-400 capitalize">{admin?.role?.replace('_', ' ')}</div>
                                    </div>
                                    <Link
                                        to="/"
                                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                                        onClick={() => setShowUserMenu(false)}
                                    >
                                        View Site
                                    </Link>
                                    <button
                                        onClick={handleLogout}
                                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                                    >
                                        Sign out
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            {showMobileMenu && (
                <div className="lg:hidden border-t border-gray-200">
                    <div className="pt-2 pb-3 space-y-1 bg-gray-50">
                        {navItems.filter(item => item.show).map((item) => (
                            <Link
                                key={item.path}
                                to={item.path}
                                onClick={() => setShowMobileMenu(false)}
                                className={combineClasses(
                                    'block pl-3 pr-4 py-3 border-l-4 text-base font-medium transition-colors duration-200',
                                    isActive(item.path)
                                        ? 'bg-teal-50 border-teal-500 text-teal-700'
                                        : 'border-transparent text-gray-600 hover:bg-gray-100 hover:border-gray-300 hover:text-gray-800'
                                )}
                            >
                                {item.label}
                            </Link>
                        ))}
                    </div>
                </div>
            )}
        </nav>
    );
};

export default AdminNav;