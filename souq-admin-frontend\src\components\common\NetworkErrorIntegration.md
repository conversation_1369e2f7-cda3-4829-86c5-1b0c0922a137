# Network Error Handling Integration Guide - Admin Panel

## Overview
This guide shows how to integrate the network error handling system into your admin panel components and API services.

## Components Created

### 1. NetworkErrorModal
- **Location**: `src/components/common/NetworkErrorModal.jsx`
- **Purpose**: Displays network error popup with retry functionality
- **Features**: 
  - Auto-detects online/offline status
  - Customizable error messages
  - Retry functionality
  - Troubleshooting tips for admin users
  - Matches admin panel theme (blue colors)

### 2. NetworkErrorProvider
- **Location**: `src/context/NetworkErrorContext.jsx`
- **Purpose**: Global context for managing network errors
- **Features**:
  - Auto-detects network status changes
  - Provides global error handling
  - Manages modal state
  - Auto-retry functionality
  - Admin-specific error messages

### 3. NetworkStatus
- **Location**: `src/components/common/NetworkStatus.jsx`
- **Purpose**: Shows connection status indicator
- **Features**:
  - Real-time connection status
  - Auto-hide when connected
  - Visual feedback for connection changes

### 4. useNetworkError Hook
- **Location**: `src/hooks/useNetworkError.js`
- **Purpose**: Easy-to-use hook for components
- **Methods**:
  - `showError()` - Show custom error
  - `handleApiError()` - Auto-handle API errors
  - `showConnectionError()` - Show connection error
  - `showServerError()` - Show server error
  - `showTimeoutError()` - Show timeout error
  - `showOfflineError()` - Show offline error
  - `showBackendDownError()` - Show backend server down error
  - `showAdminServerDownError()` - Show admin server down error

## Integration in Components

### Basic Usage
```jsx
import { useNetworkError } from '../../hooks/useNetworkError';

const MyAdminComponent = () => {
  const networkError = useNetworkError();
  const [loading, setLoading] = useState(false);

  const handleApiCall = async () => {
    setLoading(true);
    try {
      const response = await adminApi.getSomeData();
      // Handle success
    } catch (error) {
      // Let the hook handle network errors automatically
      const wasHandled = networkError.handleApiError(error, () => handleApiCall());
      if (!wasHandled) {
        // Handle other types of errors (validation, etc.)
        alert('Error: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <button onClick={handleApiCall} disabled={loading}>
        {loading ? 'Loading...' : 'Make API Call'}
      </button>
    </div>
  );
};
```

### Advanced Usage with Custom Error Handling
```jsx
const AdminComponent = () => {
  const networkError = useNetworkError();

  const handleCriticalOperation = async (data) => {
    try {
      const result = await adminApi.createImportantData(data);
      // Handle success
    } catch (error) {
      if (error.response?.status >= 500) {
        // Show server error with retry
        networkError.showServerError(() => handleCriticalOperation(data));
      } else if (error.code === 'ERR_NETWORK') {
        // Show backend down error
        networkError.showBackendDownError(() => handleCriticalOperation(data));
      } else {
        // Handle other errors normally
        alert('Operation failed: ' + error.message);
      }
    }
  };

  return (
    <button onClick={() => handleCriticalOperation(data)}>
      Perform Critical Operation
    </button>
  );
};
```

## Error Types Detected

The system automatically detects these network errors:
- **Offline**: `navigator.onLine === false`
- **Network errors**: Connection refused, DNS errors
- **Timeouts**: Request timeout errors
- **Server errors**: 5xx HTTP status codes
- **Backend down**: ERR_NETWORK, connection refused
- **Admin server down**: Admin API unavailable
- **Fetch errors**: Network request failures

## Testing Network Errors

### 1. Offline Testing:
   - Open DevTools → Network tab
   - Check "Offline" checkbox
   - Try any admin operation

### 2. Server Down Testing:
   - Stop the backend server
   - Try any admin operation
   - Should show "Backend Server Unavailable" error

### 3. Timeout Testing:
   - Use network throttling in DevTools
   - Set very slow network speeds

## Benefits

✅ **Consistent UX**: Same error handling across all admin pages
✅ **Automatic Detection**: No need to manually check error types
✅ **Retry Functionality**: Admins can easily retry failed requests
✅ **Admin-Specific Messages**: Tailored error messages for admin users
✅ **Offline Support**: Detects and handles offline scenarios
✅ **Easy Integration**: Minimal code changes required
✅ **Server Status Monitoring**: Detects when backend/admin servers are down

## Integration Checklist

- [x] NetworkErrorProvider added to App.jsx
- [x] NetworkStatus component added
- [x] NetworkErrorIntegration component added
- [x] useNetworkError hook used in components
- [x] API error handling updated
- [x] Admin-specific error messages
- [x] Server down detection
- [x] Retry functionality implemented
