import React from 'react';

const ItemDetails = ({ title, description, onTitleChange, onDescriptionChange }) => {
    return (
        <div className="space-y-4">
            <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                    Title
                </label>
                <input
                    type="text"
                    id="title"
                    value={title}
                    onChange={(e) => onTitleChange(e.target.value)}
                    placeholder="e.g. Black Levi's Jeans"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none transition-colors duration-200"
                    maxLength={80}
                />
                <div className="flex justify-end mt-1">
                    <span className="text-xs text-gray-500">{title.length}/80</span>
                </div>
            </div>

            <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                </label>
                <textarea
                    id="description"
                    value={description}
                    onChange={(e) => onDescriptionChange(e.target.value)}
                    placeholder="Describe your item: include information on fit, color, size, material, and condition."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none transition-colors duration-200 min-h-[120px]"
                    maxLength={1000}
                />
                <div className="flex justify-end mt-1">
                    <span className="text-xs text-gray-500">{description.length}/1000</span>
                </div>
            </div>
        </div>
    );
};

export default ItemDetails;
