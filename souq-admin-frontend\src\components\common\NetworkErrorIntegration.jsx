import { useEffect } from 'react';
import { useNetworkError } from '../../hooks/useNetworkError';
import { setNetworkErrorHandler } from '../../services/adminApi';

/**
 * Component that integrates network error handling with the API service
 * This component should be placed at the root level of the app
 */
const NetworkErrorIntegration = () => {
  const { handleApiError } = useNetworkError();

  useEffect(() => {
    // Set the network error handler for the API service
    setNetworkErrorHandler((error) => {
      console.log('🚨 Network error detected by API interceptor:', error);
      
      // Don't show modal for handled errors, just log them
      // The actual error handling will be done by individual components
      // This is just for logging and global error detection
      
      // You can add global error tracking here if needed
      // For example, send error reports to monitoring service
    });

    // Cleanup function
    return () => {
      setNetworkErrorHandler(null);
    };
  }, [handleApiError]);

  // This component doesn't render anything
  return null;
};

export default NetworkErrorIntegration;
