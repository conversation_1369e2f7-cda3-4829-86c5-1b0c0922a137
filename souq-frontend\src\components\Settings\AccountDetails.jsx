import React, { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { TextField, Switch } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { updateProfile } from '../../api/AuthService';
import { toast } from 'react-toastify';
import { X } from 'lucide-react';

const AccountSettings = ({ profileData, apiRefresh, seApiRefresh }) => {
    const navigate = useNavigate();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [reason, setReason] = useState("");
    const [isConfirmed, setIsConfirmed] = useState(false);

    const {
        register,
        handleSubmit,
        control,
        setValue,
        reset,
        formState: { errors }
    } = useForm({
        defaultValues: {
            email: '',
            fullName: '',
            gender: '',
            birthday: null,
            vacationMode: false
        }
    });

    useEffect(() => {
        if (profileData) {
            console.log(profileData, "profileData")
            reset({
                email: profileData.email || '',
                fullName: `${profileData.firstName || ''} ${profileData.lastName || ''}`.trim(),
                gender: profileData.gender || '',
                birthday: profileData.dateOfBirth
                    ? dayjs(profileData.dateOfBirth, 'DD/MM/YYYY')
                    : null,
                vacationMode: profileData.vacationMode || false
            });
        }
    }, [profileData, reset]);

    const handleDelete = () => {
        if (isConfirmed) {
            // Perform account deletion here
            console.log("Account deletion submitted:", { reason });
        } else {
            alert("Please confirm all orders are completed.");
        }
    };

    const onSubmit = async (data) => {
        const formattedBirthday = dayjs(data.birthday).format('DD/MM/YYYY');
        try {
            const updatedProfile = {
                dateOfBirth: formattedBirthday,
                fullName: data.fullName,
                gender: data.gender || '',
                vacationMode: data.vacationMode || false
            };
            const response = await updateProfile(updatedProfile);

            if (response.success) {
                toast.success(response.data.message)
                seApiRefresh(!apiRefresh)
            } else {
                // alert('Something went wrong. Please try again.');
            }
        } catch (error) {
            console.error(error);
            alert('Error updating profile.');
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="container space-y-6 px-4 md:px-8 py-6 mx-auto">
            {/* Email */}
            <div className="bg-white rounded-xl shadow p-4 space-y-2">
                <div className="flex justify-between items-center">
                    <div className="w-full">
                        <div className="flex items-center justify-between mt-1">
                            <span className="text-base font-medium text-gray-800">{profileData.email}</span>
                            <button
                                type="button"
                                className="ml-4 text-sm px-4 py-1 border border-teal-600 text-teal-700 font-semibold rounded hover:bg-gray-100"
                                onClick={() => navigate("/email-change", {
                                    state: {
                                        email: profileData.email
                                    }
                                })}
                            >
                                Change
                            </button>
                        </div>
                        <span className={`text-sm font-medium ${profileData.email ? 'text-green-600' : 'text-red-500'}`}>
                            {profileData.email ? 'Verified ✓' : 'Not Verified'}
                        </span>
                    </div>


                </div>

            </div>

            {/* Phone */}
            <div className="bg-white rounded-xl shadow p-4 space-y-1">
                <div className="flex justify-between items-center">
                    <p className="text-md font-medium text-gray-800">Phone number: {profileData.phone || 'Not added'}</p>
                    <button type="button" className="text-sm px-4 py-1 border border-teal-600 text-teal-700 font-semibold rounded hover:bg-gray-100" onClick={() => navigate("/send-phone-otp")}>
                        {profileData.phone ? 'Verified' : 'Verify'}
                    </button>
                </div>
                <p className="text-sm text-gray-500">
                    Your phone number will only be used to help you log in. It won’t be public or used for marketing.
                </p>
            </div>

            {/* Personal Info */}
            <div className="bg-white rounded-xl shadow p-4 space-y-6">
                <h3 className="text-md font-semibold text-gray-800">Personal Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Full name */}
                    <div>
                        <label className="text-sm font-medium text-gray-700">Full name</label>
                        <input
                            type="text"
                            {...register('fullName', { required: 'Full name is required' })}
                            className="mt-1 border px-3 py-2 rounded w-full"
                        />
                        {errors.fullName && <p className="text-xs text-red-500 mt-1">{errors.fullName.message}</p>}
                    </div>

                    {/* Gender */}
                    <div>
                        <label className="text-sm font-medium text-gray-700">Gender</label>
                        <select
                            {...register('gender', { required: 'Gender is required' })}
                            className="mt-1 border px-3 py-2 rounded w-full"
                        >
                            <option value="">Select gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="other">Other</option>
                        </select>
                        {errors.gender && <p className="text-xs text-red-500 mt-1">{errors.gender.message}</p>}
                    </div>

                    {/* Birthday */}
                    <div className="md:col-span-2 w-fit">
                        <label className="text-sm font-medium text-gray-700">Birthday</label>
                        <Controller
                            name="birthday"
                            control={control}
                            render={({ field }) => (
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        value={field.value}
                                        onChange={(date) => field.onChange(date)}
                                        openTo="day"
                                        views={['year', 'month', 'day']}
                                        format="DD/MM/YYYY"
                                        disableFuture
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                size: 'small',
                                                className: 'mt-1',
                                            },
                                        }}
                                    />
                                </LocalizationProvider>
                            )}
                        />

                    </div>
                </div>
            </div>

            {/* Vacation Mode */}
            <div className="bg-white rounded-xl shadow p-4 flex justify-between items-center">
                <label className="text-md font-medium text-gray-800">Vacation mode</label>
                <Controller
                    name="vacationMode"
                    control={control}
                    render={({ field }) => (
                        <Switch
                            checked={field.value}
                            onChange={(e) => field.onChange(e.target.checked)}
                            sx={{
                                '& .MuiSwitch-switchBase.Mui-checked': { color: '#0D9488' },
                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                    backgroundColor: '#0D9488'
                                }
                            }}
                        />
                    )}
                />
            </div>

            {/* Social Links */}
            <div className="bg-white rounded-xl shadow p-4 space-y-4">
                <div className="flex justify-between items-center">
                    <p className="text-md font-medium text-gray-800">Facebook</p>
                    <button type="button" className="text-sm px-4 py-1 border border-teal-600 text-teal-700 font-semibold rounded hover:bg-gray-100" onClick={() => {
                        if (!profileData.loginWithFacebook) {
                            window.location.href = `${import.meta.env.VITE_API_BASE_URL}/api/user/auth/facebook`;
                        }
                    }}>
                        {profileData.loginWithFacebook ? 'Linked' : 'Link'}
                    </button>
                </div>
                <div className="flex justify-between items-center">
                    <p className="text-md font-medium text-gray-800">Google</p>
                    <button type="button" className="text-sm px-4 py-1 border border-gray-400 text-gray-700 font-semibold rounded bg-gray-100" onClick={() => {
                        if (!profileData.loginWithGoogle) {
                            window.location.href = `${import.meta.env.VITE_API_BASE_URL}/api/user/auth/google`;
                        }
                    }}>
                        {profileData.loginWithGoogle ? 'Linked' : 'Link'}
                    </button>
                </div>
                <p className="text-xs text-gray-500">
                    Link to your other accounts to become a trusted, verified member.
                </p>
            </div>

            {/* Change Password */}
            <div className="bg-white rounded-xl shadow p-4 flex justify-between items-center" onClick={() => navigate("/reset-password", { state: { reset: true } })}>
                <p className="text-md font-medium text-gray-800">Change password</p>
                <button type="button" className="text-sm px-4 py-1 border border-teal-600 text-teal-700 font-semibold rounded hover:bg-gray-100">
                    Change
                </button>
            </div>

            {/* Delete Account */}
            <div className="bg-white rounded-xl shadow p-4 flex justify-between items-center cursor-pointer" onClick={() => setIsModalOpen(true)}>
                <p className="text-md font-medium text-gray-800">Delete my account</p>
                <button type="button" className="text-xl text-gray-500">›</button>
            </div>

            {/* Submit */}
            <div className="text-right space-x-2 rtl:text">
                <button
                    type="submit"
                    className="px-6 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
                >
                    Save
                </button>
            </div>
            {isModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                    <div className="bg-white p-6 rounded-xl w-full max-w-md shadow-lg">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-lg font-semibold">Delete my account</h2>
                            <button
                                onClick={() => setIsModalOpen(false)}
                                className="top-4 right-4 text-gray-500 hover:text-gray-700"
                            >
                                <X size={24} />
                            </button>
                        </div>
                        <label className="block text-sm font-medium mb-1">Help us improve</label>
                        <textarea
                            className="w-full border rounded-md p-2 mb-4"
                            rows="3"
                            placeholder="Tell us why you’re closing your account"
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                        />

                        <label className="flex items-center text-gray-600 mb-4">
                            <input
                                type="checkbox"
                                className="mr-2 w-5 h-5 accent-teal-600"
                                checked={isConfirmed}
                                onChange={() => setIsConfirmed(!isConfirmed)}
                            />
                            I confirm that all my orders are completed
                        </label>

                        <p className="text-xs text-gray-600 mb-4">
                            If you delete your account, it will be deactivated immediately. Deactivated accounts are only visible to Team Vinted before they are permanently deleted. The deletion takes place within the time frames indicated in Vinted’s <a href="#" className="text-teal-600 underline">Privacy Policy</a>.
                        </p>

                        <div className="flex justify-between">
                            <button
                                onClick={() => setIsModalOpen(false)}
                                className="text-gray-600 hover:text-black"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleDelete}
                                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                            >
                                Delete account
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </form>
    );
};

export default AccountSettings;
