import axiosInstance from './AxiosInstance';

class ShippingService {
  // Get available shipping providers
  async getProviders() {
    try {
      const response = await axiosInstance.get('/shipping/providers');
      return response.data;
    } catch (error) {
      console.error('Get providers error:', error);
      throw error.response?.data || error;
    }
  }

  // Get shipping rates
  async getShippingRates(origin, destination, packageDetails, providerName = null) {
    try {
      const response = await axiosInstance.post('/shipping/rates', {
        origin,
        destination,
        packageDetails,
        providerName
      });
      return response.data;
    } catch (error) {
      console.error('Get shipping rates error:', error);
      throw error.response?.data || error;
    }
  }

  // Create shipment
  async createShipment(orderId, providerName, serviceCode, shipmentData) {
    try {
      const response = await axiosInstance.post('/shipping/shipments', {
        orderId,
        providerName,
        serviceCode,
        shipmentData
      });
      return response.data;
    } catch (error) {
      console.error('Create shipment error:', error);
      throw error.response?.data || error;
    }
  }

  // Track shipment
  async trackShipment(trackingNumber) {
    try {
      const response = await axiosInstance.get(`/shipping/track/${trackingNumber}`);
      return response.data;
    } catch (error) {
      console.error('Track shipment error:', error);
      throw error.response?.data || error;
    }
  }

  // Get user's delivery options
  async getDeliveryOptions() {
    try {
      console.log('📡 Fetching delivery options...');
      const response = await axiosInstance.get('/shipping/delivery-options');
      console.log('📦 Delivery options response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Get delivery options error:', error);
      throw error.response?.data || error;
    }
  }

  // Save delivery option
  async saveDeliveryOption(deliveryOptionData, deliveryOptionId = null) {
    try {
      const url = deliveryOptionId 
        ? `/shipping/delivery-options/${deliveryOptionId}`
        : '/shipping/delivery-options';
      
      const method = deliveryOptionId ? 'put' : 'post';
      
      const response = await axiosInstance[method](url, {
        deliveryOptionId,
        ...deliveryOptionData
      });
      return response.data;
    } catch (error) {
      console.error('Save delivery option error:', error);
      throw error.response?.data || error;
    }
  }

  // Delete delivery option
  async deleteDeliveryOption(deliveryOptionId) {
    try {
      const response = await axiosInstance.delete(`/shipping/delivery-options/${deliveryOptionId}`);
      return response.data;
    } catch (error) {
      console.error('Delete delivery option error:', error);
      throw error.response?.data || error;
    }
  }

  // Set default delivery option
  async setDefaultDeliveryOption(deliveryOptionId) {
    try {
      const response = await axiosInstance.put(`/shipping/delivery-options/${deliveryOptionId}/default`);
      return response.data;
    } catch (error) {
      console.error('Set default delivery option error:', error);
      throw error.response?.data || error;
    }
  }

  // Get orders
  async getOrders(role = 'buyer', status = null, page = 1, limit = 10) {
    try {
      const params = { role, page, limit };
      if (status) params.status = status;
      
      const response = await axiosInstance.get('/orders', { params });
      return response.data;
    } catch (error) {
      console.error('Get orders error:', error);
      throw error.response?.data || error;
    }
  }

  // Get order details
  async getOrderDetails(orderId) {
    try {
      const response = await axiosInstance.get(`/orders/${orderId}`);
      return response.data;
    } catch (error) {
      console.error('Get order details error:', error);
      throw error.response?.data || error;
    }
  }

  // Create order
  async createOrder(orderData) {
    try {
      const response = await axiosInstance.post('/orders', orderData);
      return response.data;
    } catch (error) {
      console.error('Create order error:', error);
      throw error.response?.data || error;
    }
  }

  // Update order status
  async updateOrderStatus(orderId, status, notes = '') {
    try {
      const response = await axiosInstance.put(`/orders/${orderId}/status`, {
        status,
        notes
      });
      return response.data;
    } catch (error) {
      console.error('Update order status error:', error);
      throw error.response?.data || error;
    }
  }

  // Confirm delivery
  async confirmDelivery(orderId, rating = null, feedback = '') {
    try {
      const response = await axiosInstance.post(`/orders/${orderId}/confirm-delivery`, {
        rating,
        feedback
      });
      return response.data;
    } catch (error) {
      console.error('Confirm delivery error:', error);
      throw error.response?.data || error;
    }
  }

  // Get order statistics
  async getOrderStatistics(role = 'buyer') {
    try {
      const response = await axiosInstance.get('/orders/statistics', {
        params: { role }
      });
      return response.data;
    } catch (error) {
      console.error('Get order statistics error:', error);
      throw error.response?.data || error;
    }
  }

  // Format shipping provider display name
  formatProviderName(providerName) {
    const providerMap = {
      'aramex': 'Aramex',
      'fetchr': 'Fetchr',
      'dhl': 'DHL Express',
      'local_pickup': 'Local Pickup',
      'local_dropoff': 'Drop-off Point',
      'local_delivery': 'Local Delivery'
    };
    return providerMap[providerName] || providerName;
  }

  // Format delivery status
  formatDeliveryStatus(status) {
    const statusMap = {
      'pending_payment': 'Pending Payment',
      'paid': 'Paid',
      'processing': 'Processing',
      'shipped': 'Shipped',
      'in_transit': 'In Transit',
      'out_for_delivery': 'Out for Delivery',
      'delivered': 'Delivered',
      'cancelled': 'Cancelled',
      'returned': 'Returned',
      'refunded': 'Refunded'
    };
    return statusMap[status] || status;
  }

  // Get status color
  getStatusColor(status) {
    const colorMap = {
      'pending_payment': 'text-yellow-600',
      'paid': 'text-blue-600',
      'processing': 'text-purple-600',
      'shipped': 'text-indigo-600',
      'in_transit': 'text-blue-500',
      'out_for_delivery': 'text-orange-600',
      'delivered': 'text-green-600',
      'cancelled': 'text-red-600',
      'returned': 'text-red-500',
      'refunded': 'text-gray-600'
    };
    return colorMap[status] || 'text-gray-600';
  }

  // Calculate estimated delivery date
  calculateEstimatedDelivery(estimatedDays) {
    if (!estimatedDays) return null;
    
    const days = estimatedDays.max || estimatedDays.min || 3;
    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + days);
    
    return estimatedDate;
  }

  // Format currency
  formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  }
}

export default new ShippingService();
