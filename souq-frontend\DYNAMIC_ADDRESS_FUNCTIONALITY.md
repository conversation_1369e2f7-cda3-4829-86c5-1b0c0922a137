# Dynamic Country and City Functionality for Address Form

## Overview
Added dynamic country and city selection functionality to the address form in the settings page, replacing hardcoded dropdowns with API-driven selectors that use the provided location endpoints.

## Changes Made

### 1. Updated Address Component (`souq-frontend/src/components/Settings/Address.jsx`)

#### **Imports Added:**
```javascript
import CountrySelector from '../Location/CountrySelector';
import CitySelector from '../Location/CitySelector';
```

#### **State Management:**
- **Added new state variables:**
  ```javascript
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [selectedCity, setSelectedCity] = useState(null);
  ```

#### **Event Handlers:**
- **Country Selection Handler:**
  ```javascript
  const handleCountrySelect = (country) => {
      setSelectedCountry(country);
      setSelectedCity(null); // Clear city when country changes
      setFormData(prev => ({ 
          ...prev, 
          country: country ? country.name : '',
          city: '' // Clear city when country changes
      }));
      // Clear validation errors
  };
  ```

- **City Selection Handler:**
  ```javascript
  const handleCitySelect = (city) => {
      setSelectedCity(city);
      setFormData(prev => ({ 
          ...prev, 
          city: city ? city.name : '',
          state: city ? (city.state || '') : prev.state
      }));
      // Clear validation errors
  };
  ```

#### **Form Reset Updates:**
- **handleAddNew()**: Now resets selectedCountry and selectedCity to null
- **handleEdit()**: Resets selectors (future enhancement: populate from existing data)

### 2. UI Components Replaced

#### **Before (Hardcoded Country Dropdown):**
```javascript
<select value={formData.country} onChange={(e) => handleInputChange('country', e.target.value)}>
    <option value="United States">United States</option>
    <option value="Canada">Canada</option>
    <option value="United Kingdom">United Kingdom</option>
    <option value="Australia">Australia</option>
    <option value="Germany">Germany</option>
    <option value="France">France</option>
    <option value="Other">Other</option>
</select>
```

#### **After (Dynamic Country Selector):**
```javascript
<CountrySelector
    selectedCountry={selectedCountry}
    onCountrySelect={handleCountrySelect}
    placeholder="Select Country"
    required={true}
    error={errors.country}
    className="w-full"
/>
```

#### **Before (Text Input for City):**
```javascript
<input
    type="text"
    value={formData.city}
    onChange={(e) => handleInputChange('city', e.target.value)}
    placeholder="City"
    className="w-full border rounded p-2"
/>
```

#### **After (Dynamic City Selector):**
```javascript
<CitySelector
    selectedCountry={selectedCountry}
    selectedCity={selectedCity}
    onCitySelect={handleCitySelect}
    placeholder="Select City"
    required={true}
    error={errors.city}
    className="w-full"
/>
```

## API Endpoints Used

### **Country APIs:**
- **Get All Countries**: `GET /api/user/location/countries`
- **Get Country by Code**: `GET /api/user/location/countries/code/{countryCode}`
- **Search Countries**: `GET /api/user/location/countries/search?q={query}`

### **City APIs:**
- **Get Cities by Country**: `GET /api/user/location/cities/country/{countryId}`
- **Search Cities**: `GET /api/user/location/cities/search?q={query}&countryId={countryId}`

## Features

### 1. **Dynamic Country Selection**
- **Searchable Dropdown**: Users can search for countries by name or code
- **Flag Display**: Countries show with their flags for easy identification
- **Real-time Search**: API-powered search with 2+ character minimum
- **Validation**: Required field validation with error display

### 2. **Dynamic City Selection**
- **Country-dependent**: Cities load based on selected country
- **Searchable**: Users can search within the selected country's cities
- **State Auto-fill**: Automatically populates state/province when available
- **Cascading Reset**: Clears city when country changes

### 3. **User Experience**
- **Progressive Enhancement**: Country must be selected before city
- **Loading States**: Shows loading indicators during API calls
- **Error Handling**: Graceful fallback to local filtering if API fails
- **Validation Integration**: Works with existing form validation

### 4. **Data Flow**
```
User selects country → API call to get cities → City dropdown populates
User selects city → Form data updates with city name and state
Form submission → Validates both country and city are selected
```

## Components Used

### **CountrySelector Component**
- **Location**: `souq-frontend/src/components/Location/CountrySelector.jsx`
- **Features**: Search, flag display, validation, loading states
- **API**: Uses LocationService for country data

### **CitySelector Component**
- **Location**: `souq-frontend/src/components/Location/CitySelector.jsx`
- **Features**: Country-dependent loading, search, state auto-fill
- **API**: Uses LocationService for city data

### **LocationService**
- **Location**: `souq-frontend/src/api/LocationService.js`
- **Functions**: getCountries(), getCitiesByCountry(), searchCountries(), searchCities()

## Validation

### **Client-side Validation** (AddressService.js)
```javascript
if (!addressData.country?.trim()) {
    errors.country = 'Country is required';
}

if (!addressData.city?.trim()) {
    errors.city = 'City is required';
}
```

### **Form Validation Flow**
1. **Country Selection**: Required, validates country name is not empty
2. **City Selection**: Required, validates city name is not empty
3. **Error Display**: Shows validation errors below each selector
4. **Error Clearing**: Automatically clears errors when valid selection is made

## Usage

### **For Users**
1. **Open Address Form**: Click "Add Address" in settings
2. **Select Country**: Search and select from dynamic country list
3. **Select City**: Search and select from cities in chosen country
4. **Auto-fill**: State/province automatically filled if available
5. **Save**: Form validates both selections before saving

### **For Developers**
```javascript
// Country selection triggers city loading
const handleCountrySelect = (country) => {
    setSelectedCountry(country);
    setSelectedCity(null); // Reset city
    // Update form data with country name
};

// City selection updates form with city and state
const handleCitySelect = (city) => {
    setSelectedCity(city);
    // Update form data with city name and state
};
```

## Benefits

### **✅ Dynamic Data**
- **Real-time**: Countries and cities loaded from live API
- **Up-to-date**: Always reflects current database content
- **Searchable**: Easy to find specific locations

### **✅ Better UX**
- **Guided Selection**: Country-first approach prevents invalid combinations
- **Auto-completion**: State/province filled automatically
- **Visual Feedback**: Flags and loading states improve usability

### **✅ Data Integrity**
- **Consistent Format**: Standardized country and city names
- **Validation**: Ensures valid location combinations
- **Error Prevention**: Cascading dropdowns prevent invalid selections

### **✅ Scalability**
- **API-driven**: Easy to add new countries/cities via backend
- **Reusable**: Components can be used in other forms
- **Maintainable**: Centralized location data management

## Testing

### **Manual Testing Steps**
1. **Open Settings** → Navigate to address section
2. **Click "Add Address"** → Modal should open
3. **Country Selection** → Should load countries dynamically
4. **Search Countries** → Should filter results
5. **Select Country** → Should clear city and load cities for that country
6. **City Selection** → Should show cities for selected country
7. **Search Cities** → Should filter cities within country
8. **Form Submission** → Should validate both selections
9. **Edit Address** → Should handle existing address data

### **API Testing**
```bash
# Test country endpoint
curl "http://localhost:5000/api/user/location/countries"

# Test cities by country
curl "http://localhost:5000/api/user/location/cities/country/COUNTRY_ID"

# Test country search
curl "http://localhost:5000/api/user/location/countries/search?q=united"

# Test city search
curl "http://localhost:5000/api/user/location/cities/search?q=new&countryId=COUNTRY_ID"
```

## Future Enhancements

### **1. Edit Mode Improvement**
- **Load Existing Data**: When editing, find and set country/city objects from names
- **Backward Compatibility**: Handle addresses with old country/city format

### **2. Performance Optimization**
- **Caching**: Cache country list to reduce API calls
- **Debouncing**: Add search debouncing for better performance

### **3. Additional Features**
- **Recent Selections**: Remember recently selected countries/cities
- **Popular Locations**: Show popular countries/cities first
- **Geolocation**: Auto-detect user's country based on IP
