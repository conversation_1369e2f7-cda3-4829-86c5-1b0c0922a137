const Order = require('../../../../db/models/orderModel');
const Transaction = require('../../../../db/models/transactionModel');
const StandardPayment = require('../../../../db/models/standardPaymentModel');
const Shipment = require('../../../../db/models/shipmentModel');
const Product = require('../../../../db/models/productModel');
const User = require('../../../../db/models/userModel');

class OrderController {
  /**
   * Map payment status to order status
   */
  mapPaymentStatusToOrderStatus(paymentStatus) {
    const statusMap = {
      'completed': 'paid',
      'paid': 'paid',
      'pending': 'pending_payment',
      'processing': 'pending_payment', // Payment being processed, order still pending
      'failed': 'cancelled',
      'cancelled': 'cancelled'
    };
    return statusMap[paymentStatus] || 'pending_payment';
  }

  /**
   * Get user's orders (buyer or seller) from transactions and standardpayments
   */
  async getUserOrders(req, res) {
    try {
      const userId = req.user._id; // Use MongoDB ObjectId instead of custom UUID
      const { role = 'buyer', status, page = 1, limit = 10 } = req.query;

      console.log('🔄 Fetching orders for user:', userId, 'role:', role);

      const query = {};
      if (role === 'buyer') {
        query.buyer = userId;
      } else if (role === 'seller') {
        query.seller = userId;
      } else {
        // Both buyer and seller
        query.$or = [{ buyer: userId }, { seller: userId }];
      }

      // Add status filter if provided
      if (status) {
        query.status = status;
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const limitNum = parseInt(limit);

      // Fetch from both transactions and standardpayments
      const [transactions, standardPayments] = await Promise.all([
        // Fetch escrow transactions
        Transaction.find(query)
          .populate('product', 'title price product_photos brand size condition material colors user')
          .populate('buyer', 'username profile_picture email')
          .populate('seller', 'username profile_picture email')
          .populate('escrowTransaction')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),

        // Fetch standard payments
        StandardPayment.find(query)
          .populate('product', 'title price product_photos brand size condition material colors user')
          .populate('buyer', 'username profile_picture email')
          .populate('seller', 'username profile_picture email')
          .populate('offer')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum)
      ]);

      console.log('📦 Found transactions:', transactions.length);
      console.log('💳 Found standard payments:', standardPayments.length);

      // Combine and format the results
      const combinedOrders = [];

      // Add transactions (escrow orders)
      transactions.forEach(transaction => {
        // Map payment status to order status
        let orderStatus = transaction.orderStatus || this.mapPaymentStatusToOrderStatus(transaction.status);

        combinedOrders.push({
          _id: transaction._id,
          orderNumber: transaction.transactionId,
          type: 'escrow',
          buyer: transaction.buyer,
          seller: transaction.seller,
          product: transaction.product,
          status: orderStatus,
          orderDetails: {
            productPrice: transaction.amount,
            offerAmount: null,
            quantity: 1,
            currency: transaction.currency
          },
          payment: {
            method: 'escrow',
            status: transaction.status,
            transactionId: transaction.transactionId,
            paymentGateway: transaction.paymentGateway,
            fees: {
              total: transaction.amount
            }
          },
          shipping: {
            toAddress: transaction.escrowTransaction?.shippingAddress || null
          },
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt
        });
      });

      // Add standard payments
      standardPayments.forEach(payment => {
        // Map payment status to order status
        let orderStatus = payment.orderStatus || this.mapPaymentStatusToOrderStatus(payment.status);

        combinedOrders.push({
          _id: payment._id,
          orderNumber: payment.transactionId,
          type: 'standard',
          buyer: payment.buyer,
          seller: payment.seller,
          product: payment.product,
          status: orderStatus,
          orderDetails: {
            productPrice: payment.productPrice,
            offerAmount: payment.offer ? payment.productPrice : null,
            quantity: 1,
            currency: payment.currency
          },
          payment: {
            method: 'standard',
            status: payment.status,
            transactionId: payment.transactionId,
            paymentGateway: payment.paymentGateway,
            fees: {
              platformFee: payment.platformFeeAmount,
              shippingFee: payment.shippingCost,
              tax: payment.salesTax,
              total: payment.totalAmount
            }
          },
          shipping: {
            toAddress: payment.shippingAddress
          },
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt
        });
      });

      // Sort combined results by creation date
      combinedOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // Get total count for pagination
      const [transactionCount, standardPaymentCount] = await Promise.all([
        Transaction.countDocuments(query),
        StandardPayment.countDocuments(query)
      ]);

      const totalOrders = transactionCount + standardPaymentCount;

      console.log('✅ Total orders found:', totalOrders);

      res.json({
        success: true,
        data: {
          orders: combinedOrders,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(totalOrders / limitNum),
            totalOrders,
            hasNext: skip + combinedOrders.length < totalOrders,
            hasPrev: parseInt(page) > 1
          }
        }
      });
    } catch (error) {
      console.error('❌ Get user orders error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch orders',
        details: error.message
      });
    }
  }

  /**
   * Get order details from transactions or standardpayments
   */
  async getOrderDetails(req, res) {
    try {
      const { orderId } = req.params;
      const userId = req.user._id; // Use MongoDB ObjectId

      console.log('🔍 Looking for order:', orderId, 'for user:', userId);

      // Try to find in transactions first
      let order = await Transaction.findById(orderId)
        .populate('product', 'title price product_photos brand size condition material colors user')
        .populate('buyer', 'username profile_picture email phone')
        .populate('seller', 'username profile_picture email phone')
        .populate('escrowTransaction');

      let orderType = 'escrow';

      // If not found in transactions, try standardpayments
      if (!order) {
        order = await StandardPayment.findById(orderId)
          .populate('product', 'title price product_photos brand size condition material colors user')
          .populate('buyer', 'username profile_picture email phone')
          .populate('seller', 'username profile_picture email phone')
          .populate('offer');

        orderType = 'standard';
      }

      if (!order) {
        return res.status(404).json({
          success: false,
          error: 'Order not found'
        });
      }

      // Check if user has access to this order
      if (order.buyer._id.toString() !== userId.toString() && order.seller._id.toString() !== userId.toString()) {
        return res.status(403).json({
          success: false,
          error: 'Access denied'
        });
      }

      // Format order data based on type
      const formattedOrder = {
        _id: order._id,
        orderNumber: order.transactionId,
        type: orderType,
        buyer: order.buyer,
        seller: order.seller,
        product: order.product,
        status: order.status,
        orderDetails: {
          productPrice: orderType === 'escrow' ? order.amount : order.productPrice,
          offerAmount: orderType === 'standard' && order.offer ? order.productPrice : null,
          quantity: 1,
          currency: order.currency
        },
        payment: {
          method: orderType,
          status: order.status,
          transactionId: order.transactionId,
          paymentGateway: order.paymentGateway,
          fees: orderType === 'escrow' ? {
            total: order.amount
          } : {
            platformFee: order.platformFeeAmount,
            shippingFee: order.shippingCost,
            tax: order.salesTax,
            total: order.totalAmount
          }
        },
        shipping: {
          toAddress: orderType === 'escrow' ?
            order.escrowTransaction?.shippingAddress :
            order.shippingAddress
        },
        createdAt: order.createdAt,
        updatedAt: order.updatedAt
      };

      // Get shipment information if available (this would need to be implemented based on your shipment tracking)
      let shipment = null;
      // Note: You may need to implement shipment tracking based on transaction ID or other identifier

      console.log('✅ Order details found:', formattedOrder.orderNumber);

      res.json({
        success: true,
        data: {
          order: formattedOrder,
          shipment: shipment
        }
      });
    } catch (error) {
      console.error('❌ Get order details error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch order details',
        details: error.message
      });
    }
  }

  /**
   * Create order from accepted offer or direct purchase
   */
  async createOrder(req, res) {
    try {
      const userId = req.user._id; // Use MongoDB ObjectId
      const { 
        productId, 
        sellerId, 
        offerId, 
        offerAmount, 
        paymentMethod, 
        shippingAddress,
        paymentDetails 
      } = req.body;

      // Validate required fields
      if (!productId || !sellerId || !paymentMethod || !shippingAddress) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields'
        });
      }

      // Get product details
      const product = await Product.findById(productId).populate('user');
      if (!product) {
        return res.status(404).json({
          success: false,
          error: 'Product not found'
        });
      }

      // Verify seller
      if (product.user._id.toString() !== sellerId) {
        return res.status(400).json({
          success: false,
          error: 'Invalid seller for this product'
        });
      }

      // Calculate order details
      const productPrice = offerAmount || product.price;
      const shippingFee = 0; // Will be calculated based on selected shipping option
      const platformFee = paymentMethod === 'escrow' ? (productPrice * 0.1) : 0.85;
      const tax = 0.72;
      const total = productPrice + platformFee + shippingFee + tax;

      // Create order
      const order = new Order({
        buyer: userId,
        seller: sellerId,
        product: productId,
        orderDetails: {
          productPrice: product.price,
          offerAmount: offerAmount,
          offerId: offerId,
          quantity: 1,
          currency: 'USD'
        },
        payment: {
          method: paymentMethod,
          status: 'pending',
          fees: {
            platformFee,
            shippingFee,
            tax,
            total
          }
        },
        shipping: {
          toAddress: shippingAddress
        },
        status: 'pending_payment',
        timeline: [{
          status: 'pending_payment',
          timestamp: new Date(),
          description: 'Order created, awaiting payment',
          updatedBy: 'buyer'
        }]
      });

      await order.save();

      // Populate order for response
      await order.populate([
        { path: 'product', select: 'title price product_photos' },
        { path: 'buyer', select: 'username profile_picture' },
        { path: 'seller', select: 'username profile_picture' }
      ]);

      res.json({
        success: true,
        data: { order }
      });
    } catch (error) {
      console.error('Create order error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create order'
      });
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(req, res) {
    try {
      const { orderId } = req.params;
      const { status, notes } = req.body;
      const userId = req.user._id; // Use MongoDB ObjectId

      console.log('🔄 Updating order status:', { orderId, status, userId: userId.toString() });

      // First try to find in Order collection
      let order = await Order.findById(orderId);
      let isTransaction = false;
      let isStandardPayment = false;

      // If not found in Order collection, try Transaction collection
      if (!order) {
        console.log('📦 Order not found in Order collection, checking Transaction collection...');
        order = await Transaction.findById(orderId)
          .populate('product', 'title price product_photos brand size condition material colors user')
          .populate('buyer', 'username profile_picture email phone')
          .populate('seller', 'username profile_picture email phone');

        if (order) {
          isTransaction = true;
          console.log('✅ Found order in Transaction collection');
        }
      }

      // If not found in Transaction collection, try StandardPayment collection
      if (!order) {
        console.log('📦 Order not found in Transaction collection, checking StandardPayment collection...');
        order = await StandardPayment.findById(orderId)
          .populate('product', 'title price product_photos brand size condition material colors user')
          .populate('buyer', 'username profile_picture email phone')
          .populate('seller', 'username profile_picture email phone');

        if (order) {
          isStandardPayment = true;
          console.log('✅ Found order in StandardPayment collection');
        }
      }

      if (!order) {
        console.log('❌ Order not found in any collection');
        return res.status(404).json({
          success: false,
          error: 'Order not found'
        });
      }

      // Check permissions
      let updatedBy = 'system';
      const buyerId = order.buyer?._id || order.buyer;
      const sellerId = order.seller?._id || order.seller;

      if (buyerId && buyerId.toString() === userId.toString()) {
        updatedBy = 'buyer';
      } else if (sellerId && sellerId.toString() === userId.toString()) {
        updatedBy = 'seller';
      } else {
        console.log('❌ Access denied:', { buyerId: buyerId?.toString(), sellerId: sellerId?.toString(), userId: userId.toString() });
        return res.status(403).json({
          success: false,
          error: 'Access denied'
        });
      }

      // Get current status based on collection type
      let currentStatus = order.status;
      if (isTransaction || isStandardPayment) {
        // Check if orderStatus field exists (newly added field)
        if (order.orderStatus) {
          currentStatus = order.orderStatus;
        } else {
          // For transactions and standard payments, map payment status to order status
          // The status field in these collections refers to payment status
          if (order.status === 'completed' || order.status === 'paid') {
            currentStatus = 'paid';
          } else if (order.status === 'pending') {
            currentStatus = 'pending_payment';
          } else if (order.status === 'processing') {
            // Payment is being processed - treat as pending_payment for order status
            // but allow special transition to shipped if seller wants to ship
            currentStatus = 'pending_payment';
          } else {
            currentStatus = order.status === 'failed' || order.status === 'cancelled' ? 'cancelled' : 'pending_payment';
          }
        }
      }

      console.log('📊 Status mapping:', {
        originalStatus: order.status,
        orderStatus: order.orderStatus,
        mappedCurrentStatus: currentStatus,
        isTransaction,
        isStandardPayment
      });

      // Validate status transitions
      const validTransitions = {
        'pending_payment': ['paid', 'cancelled'],
        'paid': ['processing', 'shipped', 'cancelled'], // Allow direct paid -> shipped
        'processing': ['shipped', 'cancelled'],
        'shipped': ['in_transit', 'delivered'],
        'in_transit': ['out_for_delivery', 'delivered'],
        'out_for_delivery': ['delivered'],
        'delivered': ['returned'], // Only if there's an issue
        'cancelled': [], // Final state
        'returned': [], // Final state
        'refunded': [] // Final state
      };

      // Special case: If payment is completed or processing but order status is still pending_payment,
      // allow seller to mark as shipped (auto-transition through paid -> shipped)
      if (currentStatus === 'pending_payment' && status === 'shipped' && updatedBy === 'seller') {
        if ((isTransaction || isStandardPayment) && (order.status === 'completed' || order.status === 'processing')) {
          console.log('✅ Auto-transitioning completed/processing payment from pending_payment to shipped');
          currentStatus = 'paid'; // Treat as paid for transition validation
        }
      }

      // Check if trying to transition to the same status
      if (currentStatus === status) {
        console.log('❌ Cannot transition to same status:', { currentStatus, targetStatus: status });
        return res.status(400).json({
          success: false,
          error: `Order is already ${status}. Cannot transition from ${currentStatus} to ${status}.`
        });
      }

      if (!validTransitions[currentStatus]?.includes(status)) {
        console.log('❌ Invalid status transition:', { from: currentStatus, to: status });
        return res.status(400).json({
          success: false,
          error: `Cannot transition from ${currentStatus} to ${status}. Current payment status: ${order.status}`
        });
      }

      // Update order based on collection type
      let updatedOrder;

      if (isTransaction) {
        // Update Transaction
        const updateData = {
          orderStatus: status,
          $push: {
            statusHistory: {
              status,
              timestamp: new Date(),
              description: notes || `Order status updated to ${status}`,
              updatedBy
            }
          }
        };

        updatedOrder = await Transaction.findByIdAndUpdate(orderId, updateData, { new: true })
          .populate('product', 'title price product_photos')
          .populate('buyer', 'username profile_picture')
          .populate('seller', 'username profile_picture');

      } else if (isStandardPayment) {
        // Update StandardPayment
        const updateData = {
          orderStatus: status,
          $push: {
            statusHistory: {
              status,
              timestamp: new Date(),
              description: notes || `Order status updated to ${status}`,
              updatedBy
            }
          }
        };

        updatedOrder = await StandardPayment.findByIdAndUpdate(orderId, updateData, { new: true })
          .populate('product', 'title price product_photos')
          .populate('buyer', 'username profile_picture')
          .populate('seller', 'username profile_picture');

      } else {
        // Update Order (original logic)
        const updateData = {
          status,
          $push: {
            timeline: {
              status,
              timestamp: new Date(),
              description: notes || `Order status updated to ${status}`,
              updatedBy
            }
          }
        };

        // Add specific updates based on status
        if (status === 'delivered') {
          updateData['delivery.confirmationDate'] = new Date();
          updateData['delivery.confirmedBy'] = updatedBy;
        }

        updatedOrder = await Order.findByIdAndUpdate(orderId, updateData, { new: true })
          .populate('product', 'title price product_photos')
          .populate('buyer', 'username profile_picture')
          .populate('seller', 'username profile_picture');
      }

      console.log('✅ Order status updated successfully');

      res.json({
        success: true,
        data: { order: updatedOrder }
      });
    } catch (error) {
      console.error('❌ Update order status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update order status',
        details: error.message
      });
    }
  }

  /**
   * Confirm delivery (buyer only)
   */
  async confirmDelivery(req, res) {
    try {
      const { orderId } = req.params;
      const { rating, feedback } = req.body;
      const userId = req.user._id; // Use MongoDB ObjectId

      console.log('🔄 Confirming delivery:', { orderId, userId: userId.toString() });

      // First try to find in Order collection
      let order = await Order.findById(orderId);
      let isTransaction = false;
      let isStandardPayment = false;

      // If not found in Order collection, try Transaction collection
      if (!order) {
        console.log('📦 Order not found in Order collection, checking Transaction collection...');
        order = await Transaction.findById(orderId)
          .populate('product', 'title price product_photos')
          .populate('buyer', 'username profile_picture')
          .populate('seller', 'username profile_picture');

        if (order) {
          isTransaction = true;
          console.log('✅ Found order in Transaction collection');
        }
      }

      // If not found in Transaction collection, try StandardPayment collection
      if (!order) {
        console.log('📦 Order not found in Transaction collection, checking StandardPayment collection...');
        order = await StandardPayment.findById(orderId)
          .populate('product', 'title price product_photos')
          .populate('buyer', 'username profile_picture')
          .populate('seller', 'username profile_picture');

        if (order) {
          isStandardPayment = true;
          console.log('✅ Found order in StandardPayment collection');
        }
      }

      if (!order) {
        console.log('❌ Order not found in any collection');
        return res.status(404).json({
          success: false,
          error: 'Order not found'
        });
      }

      // Only buyer can confirm delivery
      const buyerId = order.buyer?._id || order.buyer;
      if (!buyerId || buyerId.toString() !== userId.toString()) {
        console.log('❌ Access denied - not buyer:', { buyerId: buyerId?.toString(), userId: userId.toString() });
        return res.status(403).json({
          success: false,
          error: 'Only the buyer can confirm delivery'
        });
      }

      // Get current status based on collection type
      let currentStatus = order.status;
      if (isTransaction || isStandardPayment) {
        // Check if orderStatus field exists (newly added field)
        if (order.orderStatus) {
          currentStatus = order.orderStatus;
        } else {
          // For transactions and standard payments, map payment status to order status
          if (order.status === 'completed' || order.status === 'paid') {
            currentStatus = 'paid';
          } else if (order.status === 'pending') {
            currentStatus = 'pending_payment';
          } else if (order.status === 'processing') {
            // Payment is being processed - treat as pending_payment for order status
            currentStatus = 'pending_payment';
          } else {
            currentStatus = order.status === 'failed' || order.status === 'cancelled' ? 'cancelled' : 'pending_payment';
          }
        }
      }

      console.log('📊 Delivery confirmation status mapping:', {
        originalStatus: order.status,
        orderStatus: order.orderStatus,
        mappedCurrentStatus: currentStatus
      });

      // Check if order is in a deliverable state
      if (!['shipped', 'in_transit', 'out_for_delivery', 'delivered'].includes(currentStatus)) {
        console.log('❌ Order not in deliverable state:', currentStatus);
        return res.status(400).json({
          success: false,
          error: 'Order is not in a deliverable state'
        });
      }

      // Update order based on collection type
      let updatedOrder;

      if (isTransaction) {
        // Update Transaction
        const updateData = {
          orderStatus: 'delivered',
          deliveryConfirmedAt: new Date(),
          deliveryConfirmedBy: 'buyer',
          $push: {
            statusHistory: {
              status: 'delivered',
              timestamp: new Date(),
              description: 'Delivery confirmed by buyer',
              updatedBy: 'buyer'
            }
          }
        };

        if (rating) {
          updateData.deliveryRating = rating;
          updateData.deliveryFeedback = feedback;
          updateData.ratedAt = new Date();
        }

        updatedOrder = await Transaction.findByIdAndUpdate(orderId, updateData, { new: true })
          .populate('product', 'title price product_photos')
          .populate('seller', 'username profile_picture');

      } else if (isStandardPayment) {
        // Update StandardPayment
        const updateData = {
          orderStatus: 'delivered',
          deliveryConfirmedAt: new Date(),
          deliveryConfirmedBy: 'buyer',
          $push: {
            statusHistory: {
              status: 'delivered',
              timestamp: new Date(),
              description: 'Delivery confirmed by buyer',
              updatedBy: 'buyer'
            }
          }
        };

        if (rating) {
          updateData.deliveryRating = rating;
          updateData.deliveryFeedback = feedback;
          updateData.ratedAt = new Date();
        }

        updatedOrder = await StandardPayment.findByIdAndUpdate(orderId, updateData, { new: true })
          .populate('product', 'title price product_photos')
          .populate('seller', 'username profile_picture');

      } else {
        // Update Order (original logic)
        const updateData = {
          status: 'delivered',
          'delivery.confirmationDate': new Date(),
          'delivery.confirmedBy': 'buyer',
          $push: {
            timeline: {
              status: 'delivered',
              timestamp: new Date(),
              description: 'Delivery confirmed by buyer',
              updatedBy: 'buyer'
            }
          }
        };

        if (rating) {
          updateData['delivery.rating.deliveryRating'] = rating;
          updateData['delivery.rating.feedback'] = feedback;
          updateData['delivery.rating.ratedAt'] = new Date();
        }

        updatedOrder = await Order.findByIdAndUpdate(orderId, updateData, { new: true })
          .populate('product', 'title price product_photos')
          .populate('seller', 'username profile_picture');
      }

      console.log('✅ Delivery confirmed successfully');

      res.json({
        success: true,
        data: { order: updatedOrder }
      });
    } catch (error) {
      console.error('❌ Confirm delivery error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to confirm delivery',
        details: error.message
      });
    }
  }

  /**
   * Get order statistics for user
   */
  async getOrderStatistics(req, res) {
    try {
      const userId = req.user._id; // Use MongoDB ObjectId
      const { role = 'buyer' } = req.query;

      const matchQuery = role === 'buyer' ? { buyer: userId } : { seller: userId };

      const stats = await Order.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalValue: { $sum: '$payment.fees.total' }
          }
        }
      ]);

      const totalOrders = await Order.countDocuments(matchQuery);
      const totalValue = await Order.aggregate([
        { $match: matchQuery },
        { $group: { _id: null, total: { $sum: '$payment.fees.total' } } }
      ]);

      res.json({
        success: true,
        data: {
          totalOrders,
          totalValue: totalValue[0]?.total || 0,
          statusBreakdown: stats,
          role
        }
      });
    } catch (error) {
      console.error('Get order statistics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch order statistics'
      });
    }
  }
}

module.exports = new OrderController();
