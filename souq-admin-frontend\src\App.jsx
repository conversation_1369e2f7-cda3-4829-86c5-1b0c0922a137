import React from 'react';
import { Routes, Route, useLocation, Navigate } from 'react-router-dom';
import Header from './components/Header/Header';
import AdminNav from './components/Header/Admin/AdminNav';
import HomePage from './pages/HomePage';
import Footer from './components/Footer/Footer';
import { AppProvider } from './context/AppContext';
import { AdminAuthProvider, useAdminAuth } from './context/AdminAuthContext';
import { NetworkErrorProvider } from './context/NetworkErrorContext';
import ProductDetails from './pages/ProductDetails';

// Admin Pages
import Dashboard from './pages/admin/Dashboard';
import Products from './pages/admin/Products';
import ProductForm from './pages/admin/ProductForm';
import Categories from './pages/admin/Categories';
import Menus from './pages/admin/Menus';
import SizeManagement from './pages/admin/SizeManagement';
import NetworkStatus from './components/common/NetworkStatus';
import NetworkErrorIntegration from './components/common/NetworkErrorIntegration';

// New Admin Pages
import AdminLogin from './pages/admin/AdminLogin';
import AdminSignup from './pages/admin/AdminSignup';
import UserManagement from './pages/admin/UserManagement';
import UserDetail from './pages/admin/UserDetail';
import ListingManagement from './pages/admin/ListingManagement';
import ListingDetail from './pages/admin/ListingDetail';
import DisputeManagement from './pages/admin/DisputeManagement';
import CounterfeitManagement from './pages/admin/CounterfeitManagement';
import Analytics from './pages/admin/Analytics';
import LocationManagement from './pages/admin/LocationManagement';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAdminAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return isAuthenticated ? children : <Navigate to="/admin/login" />;
};

// Public Route Component (redirect if authenticated)
const PublicRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAdminAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return !isAuthenticated ? children : <Navigate to="/admin" />;
};

// Title management component
const PageTitle = () => {
  const location = useLocation();

  React.useEffect(() => {
    const path = location.pathname;
    let title = 'SOUQ E-commerce';

    if (path.startsWith('/admin')) {
      if (path === '/admin') {
        title = 'Admin | Dashboard';
      } else if (path.startsWith('/admin/products')) {
        title = 'Admin | Products';
      } else if (path.startsWith('/admin/categories')) {
        title = 'Admin | Categories';
      } else if (path.startsWith('/admin/menus')) {
        title = 'Admin | Menus';
      } else if (path.startsWith('/admin/sizes')) {
        title = 'Admin | Size Management';
      } else if (path.startsWith('/admin/locations')) {
        title = 'Admin | Location Management';
      } else if (path.startsWith('/admin/users')) {
        title = 'Admin | Users';
      } else if (path.startsWith('/admin/listings')) {
        title = 'Admin | Listings';
      } else if (path.startsWith('/admin/disputes')) {
        title = 'Admin | Disputes';
      } else if (path.startsWith('/admin/counterfeit')) {
        title = 'Admin | Counterfeit';
      } else if (path.startsWith('/admin/analytics')) {
        title = 'Admin | Analytics';
      } else if (path.startsWith('/admin/login')) {
        title = 'Admin | Login';
      } else if (path.startsWith('/admin/signup')) {
        title = 'Admin | Sign Up';
      }
    } else if (path.startsWith('/product/')) {
      const productId = path.split('/').pop();
      // Fetch product name from your backend or context
      const productName = getProductName(productId); // You'll need to implement this
      title = `${productName} | SOUQ E-commerce`;
    }

    document.title = title;
  }, [location]);

  return null;
};

// Layout component for pages with header and footer
const MainLayout = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
    </div>
  );
};

// Admin navigation component

// Layout component for admin pages without header and footer
const AdminLayout = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <AdminNav />
      <main className="flex-grow">
        {children}
      </main>
    </div>
  );
};

function AppContent() {
  return (
    <AppProvider>
      <NetworkErrorProvider>
        <PageTitle />
        <NetworkStatus />
        <NetworkErrorIntegration />
        <Routes>
        {/* Public Admin Routes */}
        <Route path="/admin/login" element={
          <PublicRoute>
            <AdminLogin />
          </PublicRoute>
        } />
        <Route path="/admin/signup" element={
          <PublicRoute>
            <AdminSignup />
          </PublicRoute>
        } />

        {/* Protected Admin Routes */}
        <Route path="/admin" element={
          <ProtectedRoute>
            <AdminLayout><Dashboard /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/products" element={
          <ProtectedRoute>
            <AdminLayout><Products /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/products/new" element={
          <ProtectedRoute>
            <AdminLayout><ProductForm /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/products/:id/edit" element={
          <ProtectedRoute>
            <AdminLayout><ProductForm /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/categories" element={
          <ProtectedRoute>
            <AdminLayout><Categories /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/menus" element={
          <ProtectedRoute>
            <AdminLayout><Menus /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/sizes" element={
          <ProtectedRoute>
            <AdminLayout><SizeManagement /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/locations" element={
          <ProtectedRoute>
            <AdminLayout><LocationManagement /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/users" element={
          <ProtectedRoute>
            <AdminLayout><UserManagement /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/users/:userId" element={
          <ProtectedRoute>
            <AdminLayout><UserDetail /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/listings" element={
          <ProtectedRoute>
            <AdminLayout><ListingManagement /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/listings/:id" element={
          <ProtectedRoute>
            <AdminLayout><ListingDetail /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/disputes" element={
          <ProtectedRoute>
            <AdminLayout><DisputeManagement /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/counterfeit" element={
          <ProtectedRoute>
            <AdminLayout><CounterfeitManagement /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/analytics" element={
          <ProtectedRoute>
            <AdminLayout><Analytics /></AdminLayout>
          </ProtectedRoute>
        } />
        <Route path="/admin/product/:id" element={
          <ProtectedRoute>
            <AdminLayout><ProductDetails /></AdminLayout>
          </ProtectedRoute>
        } />

        {/* Main routes with header and footer */}
        <Route path="/" element={<MainLayout><HomePage /></MainLayout>} />
        <Route path="/product/:id" element={<MainLayout><ProductDetails /></MainLayout>} />
      </Routes>
      </NetworkErrorProvider>
    </AppProvider>
  );
}

function App() {
  return (
    <AdminAuthProvider>
      <AppContent />
    </AdminAuthProvider>
  );
}

export default App;
