import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, X, Loader2 } from 'lucide-react';
import { useAppContext } from '../../context/AppContext';
import { useLocation, useNavigate } from 'react-router-dom';
import { register as registerAPI } from "../../api/AuthService"; // adjust the path
import { saveTokens } from '../../utils/TokenStorage';
import { toast } from 'react-toastify';
import { Alert } from '@mui/material';

const SignUpModal = () => {
    const { showSignUp, setShowSignUp } = useAppContext();
    const [showPassword, setShowPassword] = useState(false);
    const navigate = useNavigate()

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting, isValid },
        reset,
        watch,
        clearErrors,
        setError // <-- add this
    } = useForm({
        mode: 'onChange',
    });

    const [apiError, setApiError] = useState('');

    const onSubmit = async (data) => {
        try {
            const { agreeTerms, promoOptIn, ...dataToSend } = data;

            const res = await registerAPI(dataToSend);

            if (res.success) {
                const { accessToken, refreshToken } = res.data.data;
                saveTokens({ accessToken, refreshToken });

                setApiError(''); // Clear error on success
                navigate("/email-verify", { state: { email: data.email } });
                setShowSignUp(false);
            } else {
                setApiError(res.error || "Registration failed");
            }
        } catch (err) {
            setApiError("Something went wrong. Please try again.");
            console.error('Error:', err);
        }
    };

    const handleClose = () => {
        setShowSignUp(false);
        reset();
        clearErrors();
        setApiError(false)
        // setSubmitted(false);
    };

    if (!showSignUp) return null;

    return (
        <div className="fixed inset-0 z-50 bg-black/40 flex justify-center items-start pt-20 overflow-y-auto">
            <div className="bg-white rounded-2xl w-[420px] max-w-full p-6 relative">
                <button
                    onClick={handleClose}
                    className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                    aria-label="Close signup modal"
                >
                    <X size={24} />
                </button>

                <h2 className="text-2xl font-semibold text-center mb-6">Sign up with email</h2>
                {apiError && (
                    <Alert severity="error" className="mb-4">
                        {apiError}
                    </Alert>
                )}
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-3 text-[15px]">
                    {/* Full Name */}
                    <div>
                        <input
                            {...register('fullName', {
                                required: 'Full name is required',
                                validate: (value) => {
                                    const parts = value.trim().split(' ');
                                    return parts.length >= 2 && parts.every(part => part.length > 0)
                                        ? true
                                        : 'Enter your first and last names';
                                }
                            })}
                            className={`w-full mt-1 border px-3 py-2 rounded-lg focus:outline-none text-base font-medium ${errors.fullName ? 'border-red-500' : 'border-gray-300'
                                }`}
                            placeholder="Full name"
                        />
                        {!errors.fullName ? (
                            <p className="text-sm text-gray-500 mt-1">Your full name will not be publicly visible.</p>
                        ) : (
                            <p className="text-red-500 text-sm">{errors.fullName.message}</p>
                        )}
                    </div>


                    {/* Username */}
                    <div>
                        <input
                            {...register('userName', { required: 'Username is required' })}
                            className={`w-full mt-1 border px-3 py-2 rounded-lg focus:outline-none text-base font-medium ${errors.userName ? 'border-red-500' : 'border-gray-300'
                                }`}
                            placeholder="Username"
                        />
                        {!errors.userName ? (
                            <p className="text-sm text-gray-500 mt-1">Use only letters and numbers. Usernames can’t be changed later.</p>
                        ) : (
                            <p className="text-red-500 text-sm">{errors.userName.message}</p>
                        )}
                    </div>

                    {/* Email */}
                    <div>
                        <input
                            type="email"
                            {...register('email', {
                                required: 'Email is required',
                                pattern: {
                                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                                    message: 'Enter a valid email address',
                                },
                            })}
                            className={`w-full border px-3 py-2 rounded-lg focus:outline-none text-base font-medium ${errors.email ? 'border-red-500' : 'border-gray-300'
                                }`}
                            placeholder="Email"
                        />
                        {!errors.email ? (
                            <p className="text-sm text-gray-500 mb-1">Enter the email you want to use on Vinted</p>
                        ) : (
                            <p className="text-red-500 text-sm">{errors.email.message}</p>
                        )}
                    </div>

                    {/* Password */}
                    <div className="relative">
                        <input
                            type={showPassword ? 'text' : 'password'}
                            {...register('password', {
                                required: 'Password is required',
                                minLength: {
                                    value: 7,
                                    message: 'Password must be at least 7 characters long',
                                },
                                pattern: {
                                    value: /^(?=.*[A-Za-z])(?=.*\d).+$/,
                                    message: 'Must contain at least 1 letter and 1 number',
                                },
                            })}
                            className={`w-full mt-1 border px-3 py-2 rounded-lg focus:outline-none text-base font-medium ${errors.password ? 'border-red-500' : 'border-gray-300'}`}
                            placeholder="Password"
                        />
                        {/* <span
                            onClick={() => setShowPassword(!showPassword)}
                            className="bsolute right-3 top-2.5 cursor-pointer text-xl text-gray-600"
                        >
                            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                        </span> */}

                        <span
                            onClick={() => setShowPassword((prev) => !prev)}
                            className="absolute right-3 top-3.5 cursor-pointer text-xl text-gray-600"
                        >
                            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                        </span>
                        {!errors.password ? (
                            <p className="text-sm text-gray-500 mt-1">At least 7 characters, with at least 1 letter and 1 number.</p>
                        ) : (
                            <p className="text-red-500 text-sm">{errors.password.message}</p>
                        )}
                    </div>

                    {/* Checkboxes */}
                    <div className="space-y-3 mt-2 text-lg text-gray-500">
                        <label className="flex items-start gap-2">
                            <input
                                type="checkbox"
                                {...register('promoOptIn')}
                                className="mt-1 accent-teal-600 w-8 h-8"
                            />
                            <span>I’d like to receive personalized offers and updates via email.</span>
                        </label>

                        <label className="flex items-start gap-2">
                            <input
                                type="checkbox"
                                {...register('agreeTerms', { required: true })}
                                className="mt-1 accent-teal-600 w-8 h-8"
                            />
                            <span>
                                By clicking continue, I agree to{' '}
                                <a href="#" className="text-teal-600 underline">Souq Terms & Conditions</a>
                            </span>
                        </label>
                        {errors.agreeTerms && (
                            <p className="text-red-500 text-sm">Please accept the terms and conditions.</p>
                        )}
                    </div>

                    {/* Submit Button */}
                    <button
                        type="submit"
                        disabled={!isValid || isSubmitting}
                        className="w-full bg-teal-600 hover:bg-teal-700 text-white py-2.5 mt-3 rounded-lg font-semibold flex justify-center items-center gap-2 disabled:opacity-60"
                    >
                        {isSubmitting && <Loader2 className="animate-spin" size={18} />}
                        Continue
                    </button>
                </form>
            </div>
        </div>

    );
};

export default SignUpModal;
