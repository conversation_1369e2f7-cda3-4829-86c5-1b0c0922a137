import { useState, useEffect, useRef, useCallback } from 'react';
import { io } from 'socket.io-client';
import { getAccessToken } from '../utils/TokenStorage';

const useWebSocketChat = () => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState(null);
  const [messages, setMessages] = useState([]);
  const [currentChat, setCurrentChat] = useState(null);
  const [typing, setTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [messageError, setMessageError] = useState(null);
  
  const socketRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const initializingRef = useRef(false);

  // Initialize socket connection
  const initializeSocket = useCallback(() => {
    // Prevent multiple initializations
    if (initializingRef.current) {
      console.log('🔄 Socket initialization already in progress, skipping');
      return;
    }

    if (socketRef.current && socketRef.current.connected) {
      console.log('🔄 Socket already connected, skipping initialization');
      return socketRef.current;
    }

    initializingRef.current = true;

    // Disconnect existing socket if any
    if (socketRef.current) {
      console.log('🔌 Disconnecting existing socket');
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    const token = getAccessToken();
    if (!token) {
      setError('No authentication token found');
      initializingRef.current = false;
      return;
    }

    const baseURL = import.meta.env.VITE_API_BASE_URL;
    // Remove /api from the URL and use the base server URL for socket connection
    const socketURL = baseURL.replace('/api', '');
    
    console.log('🚀 Initializing new socket connection to:', socketURL);
    const newSocket = io(socketURL, {
      auth: { token },
      transports: ['websocket', 'polling'],
      forceNew: true, // Force a new connection
      timeout: 10000, // 10 second timeout
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 1000
    });

    newSocket.on('connect', () => {
      console.log('✅ Connected to chat server');
      setConnected(true);
      setError(null);
      initializingRef.current = false; // Reset initialization flag
    });

    newSocket.on('disconnect', (reason) => {
      console.log('❌ Disconnected from chat server:', reason);
      setConnected(false);
      if (reason === 'io server disconnect') {
        // Server disconnected the client, try to reconnect
        newSocket.connect();
      }
    });

    newSocket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      setError(`Connection failed: ${error.message}`);
      setConnected(false);
      initializingRef.current = false; // Reset initialization flag
    });

    newSocket.on('error', (error) => {
      console.error('❌ Socket error:', error);
      const errorMessage = error.message || 'Connection error';

      // Differentiate between connection errors and message errors
      if (errorMessage.includes('Image') || errorMessage.includes('Message') || errorMessage.includes('content') ||
          errorMessage.includes('text') || errorMessage.includes('data') || errorMessage.includes('format') ||
          errorMessage.includes('size') || errorMessage.includes('permission') || errorMessage.includes('Validation')) {
        setMessageError(errorMessage);

        // Mark any sending messages as failed
        setMessages(prev => prev.map(msg => {
          if (msg.status === 'sending') {
            // Mark the most recent sending message as failed
            const sendingMessages = prev.filter(m => m.status === 'sending');
            const isLatestSending = sendingMessages.length > 0 && msg.id === sendingMessages[sendingMessages.length - 1].id;

            if (isLatestSending) {
              return { ...msg, status: 'failed', error: errorMessage };
            }
          }
          return msg;
        }));

        // Clear message error after 8 seconds
        setTimeout(() => setMessageError(null), 8000);
      } else {
        setError(errorMessage);
      }
    });

    newSocket.on('new_message', (messageData) => {
      console.log('📨 New message received:', messageData);
      setMessages(prev => {
        // Check if this message already exists (to avoid duplicates from optimistic updates)
        const existingMessage = prev.find(msg => {
          // First check for exact ID match
          if (msg.id === messageData.id) return true;

          // Then check for temporary messages that should be replaced
          if (msg.id && msg.id.startsWith('temp-')) {
            // For text messages
            if (messageData.messageType === 'text' && msg.messageType === 'text') {
              return msg.text === messageData.text &&
                     msg.sender.id === messageData.sender.id &&
                     Math.abs(new Date(msg.createdAt) - new Date(messageData.createdAt)) < 30000; // 30 seconds
            }
            // For image messages
            if (messageData.messageType === 'image' && msg.messageType === 'image') {
              return msg.sender.id === messageData.sender.id &&
                     Math.abs(new Date(msg.createdAt) - new Date(messageData.createdAt)) < 30000; // 30 seconds
            }
          }

          return false;
        });

        if (existingMessage) {
          console.log('🔄 Replacing optimistic message with real message');
          // Replace the optimistic message with the real one
          return prev.map(msg =>
            msg.id === existingMessage.id ? messageData : msg
          );
        } else {
          console.log('➕ Adding new message to list');
          // Add new message
          return [...prev, messageData];
        }
      });
    });

    newSocket.on('message_updated', (messageData) => {
      console.log('🔄 Message updated:', messageData);
      setMessages(prev => {
        return prev.map(msg => {
          if (msg.id === messageData.id || msg._id === messageData.id) {
            return { ...messageData };
          }
          return msg;
        });
      });
    });

    newSocket.on('user_typing', (data) => {
      const currentUser = JSON.parse(localStorage.getItem('user'));
      const currentUserId = currentUser?.id || currentUser?._id;
      if (data.user.id !== currentUserId) {
        setOtherUserTyping(data.isTyping);
      }
    });

    newSocket.on('messages_seen', (data) => {
      setMessages(prev => prev.map(msg => ({
        ...msg,
        seen: msg.sender.id === data.seenBy ? true : msg.seen
      })));
    });

    newSocket.on('user_joined', (data) => {
      console.log(`${data.user.userName} joined the chat`);
    });

    newSocket.on('user_left', (data) => {
      console.log(`${data.user.userName} left the chat`);
    });

    // Handle offer expiration
    newSocket.on('offer_expired', (data) => {
      console.log('⏰ Offer expired:', data);
      // The expiration message will be handled by the new_message event
      // This event can be used for additional UI updates if needed
    });

    socketRef.current = newSocket;
    setSocket(newSocket);

    return newSocket;
  }, []);

  // Join a chat room
  const joinChat = useCallback((chatId, roomId) => {
    if (socketRef.current && connected) {
      console.log('🚪 Joining chat room:', { chatId, roomId });
      socketRef.current.emit('join_chat', { chatId, roomId });
      setCurrentChat({ chatId, roomId });
    } else {
      console.warn('⚠️ Cannot join chat - socket not connected');
    }
  }, [connected]);

  // Leave current chat room
  const leaveChat = useCallback(() => {
    if (socketRef.current && currentChat) {
      console.log('🚪 Leaving chat room:', currentChat);
      socketRef.current.emit('leave_chat', currentChat);
      setCurrentChat(null);
      setMessages([]);
    }
  }, [currentChat]);

  // Clear messages (useful when switching chats)
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // Send a message
  const sendMessage = useCallback((messageData) => {
    // Clear any previous message errors
    setMessageError(null);

    if (socketRef.current && currentChat && connected) {
      const messagePayload = {
        chatId: currentChat.chatId,
        roomId: currentChat.roomId,
        text: messageData.text || '',
        messageType: messageData.messageType || 'text',
        // Include imageUrl for image messages
        ...(messageData.messageType === 'image' && messageData.imageUrl && {
          imageUrl: messageData.imageUrl
        })
      };

      // Debug logging
      console.log('📤 Sending message:', {
        chatId: messagePayload.chatId,
        roomId: messagePayload.roomId,
        text: messagePayload.text,
        messageType: messagePayload.messageType,
        hasImageUrl: !!messagePayload.imageUrl,
        imageUrlType: typeof messagePayload.imageUrl,
        imageUrlLength: messagePayload.imageUrl ? messagePayload.imageUrl.length : 0
      });

      socketRef.current.emit('send_message', messagePayload);
    } else {
      const errorMsg = 'Cannot send message - not connected to chat';
      console.warn('⚠️', errorMsg, {
        hasSocket: !!socketRef.current,
        hasCurrentChat: !!currentChat,
        connected,
        currentChat
      });
      setMessageError(errorMsg);
    }
  }, [currentChat, connected]);

  // Handle typing indicators
  const handleTyping = useCallback((isTyping) => {
    if (socketRef.current && currentChat && connected) {
      const eventName = isTyping ? 'typing_start' : 'typing_stop';
      socketRef.current.emit(eventName, {
        chatId: currentChat.chatId,
        roomId: currentChat.roomId
      });
      setTyping(isTyping);

      // Clear typing after 3 seconds of inactivity
      if (isTyping) {
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
        typingTimeoutRef.current = setTimeout(() => {
          handleTyping(false);
        }, 3000);
      }
    }
  }, [currentChat, connected]);

  // Mark messages as seen
  const markAsSeen = useCallback(() => {
    if (socketRef.current && currentChat && connected) {
      socketRef.current.emit('mark_seen', {
        chatId: currentChat.chatId,
        roomId: currentChat.roomId
      });
    }
  }, [currentChat, connected]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
    if (socketRef.current) {
      console.log('🧹 Cleaning up socket connection');
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    initializingRef.current = false;
    setConnected(false);
    setCurrentChat(null);
    setMessages([]);
  }, []);

  // Initialize socket on mount - only once
  useEffect(() => {
    initializeSocket();

    return cleanup;
  }, []); // Empty dependency array - only run once on mount

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentChat) {
        leaveChat();
      }
    };
  }, []); // Empty dependency array - only run once on mount

  return {
    socket,
    connected,
    error,
    messageError,
    setMessageError,
    messages,
    setMessages,
    clearMessages,
    currentChat,
    typing,
    otherUserTyping,
    joinChat,
    leaveChat,
    sendMessage,
    handleTyping,
    markAsSeen,
    initializeSocket
  };
};

export default useWebSocketChat;
