import React, { useEffect, useRef, useState } from "react";
import { FaArrowLeft, FaArrowRight, FaTimes } from "react-icons/fa";
import { MdRadioButtonChecked, MdRadioButtonUnchecked } from "react-icons/md";
import { User, Scissors, Smile, Home as HomeIcon, Palette } from "lucide-react";
import { categories } from "../../data/categories";
import { useDispatch, useSelector } from "react-redux";
import {
  setCategory,
  setSubcategory,
  setItem,
  resetCategory,
} from "../../redux/slices/CategorySlice";
import { LuChevronDown, LuChevronUp } from "react-icons/lu";

const categoryIcons = {
  women: <User size={18} className="text-teal-600" />,
  men: <Scissors size={18} className="text-teal-600" />,
  kids: <Smile size={18} className="text-teal-600" />,
  home: <HomeIcon size={18} className="text-teal-600" />,
  beauty: <Palette size={18} className="text-teal-600" />,
};

export default function Category() {
  const dispatch = useDispatch();
  const { selectedCategory, selectedSubcategory, selectedItem } = useSelector(
    (state) => state.category
  );
  const dropdownRef = useRef(null);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  //   console.log(selectedCategory?.name,"selectedCategory")
  //   console.log(selectedSubcategory?.name,"selectedSubcategory")
  //   console.log(selectedItem?.name,"selectedItem")

  const [open, setOpen] = useState(false);
  const [step, setStep] = useState(1);

  const handleOpenCategory = () => {
    if (!open) {
      if (!selectedCategory || selectedCategory.id === "all") {
        dispatch(resetCategory());
        setStep(1);
      } else {
        setStep(2);
      }
    }
    setOpen(!open);
  };

  return (
    <>
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={handleOpenCategory}
          className="border px-4 py-2 rounded-full bg-white shadow flex items-center gap-2"
        >
          Category
          {open ? (
            <LuChevronUp className="text-gray-600 w-5 h-5" />
          ) : (
            <LuChevronDown className="text-gray-600 w-5 h-5" />
          )}
        </button>

        {open && (
          <div className="absolute z-10 w-80 bg-white shadow-lg rounded mt-2">
            {step === 1 &&
              categories.map((category) => (
                <div
                  key={category.id}
                  className="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    dispatch(setCategory(category));
                    setStep(2);
                  }}
                >
                  <div className="flex items-center gap-2">
                    {categoryIcons[category.name.toLowerCase()] || (
                      <span className="text-gray-400">❓</span>
                    )}
                    <span>{category.name}</span>
                  </div>
                  <FaArrowRight className="text-gray-400" />
                </div>
              ))}

            {step === 2 && selectedCategory && selectedCategory.id !== "all" && (
              <>
                <div className="flex items-center justify-between p-3 border-b bg-gray-100">
                  <FaArrowLeft
                    className="cursor-pointer text-gray-600"
                    onClick={() => {
                      setStep(1);
                      dispatch(setCategory(null));
                    }}
                  />
                  <span className="text-center flex-1 font-medium">
                    {selectedCategory.name}
                  </span>
                  <span className="w-5" />
                </div>

                <div
                  className="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    const allItems = selectedCategory.subcategories.flatMap(
                      (sub) => sub.items
                    );
                    const all = {
                      id: "all",
                      name: "All",
                      items: allItems,
                    };
                    dispatch(setSubcategory(all));
                    dispatch(setItem({ id: "all", name: "All" }));
                    setOpen(false);
                  }}
                >
                  <span>All</span>
                  {selectedItem?.id === "all" ? (
                    <MdRadioButtonChecked className="text-teal-600" />
                  ) : (
                    <MdRadioButtonUnchecked className="text-teal-600" />
                  )}
                </div>

                {selectedCategory.subcategories.map((subcat) => (
                  <div
                    key={subcat.id}
                    className="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                    onClick={() => {
                      dispatch(setSubcategory(subcat));
                      setStep(3);
                    }}
                  >
                    <span>{subcat.name}</span>
                    <FaArrowRight className="text-gray-400" />
                  </div>
                ))}
              </>
            )}

            {step === 3 && selectedSubcategory && selectedSubcategory.id !== "all" && (
              <>
                <div className="flex items-center justify-between p-3 border-b bg-gray-100">
                  <FaArrowLeft
                    className="cursor-pointer text-gray-600"
                    onClick={() => {
                      setStep(2);
                      dispatch(setSubcategory(null));
                    }}
                  />
                  <span className="text-center flex-1 font-medium">
                    {selectedSubcategory.name}
                  </span>
                  <span className="w-5" />
                </div>

                <div
                  className="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    dispatch(setItem({ id: "all", name: "All" }));
                    setOpen(false);
                  }}
                >
                  <span>All</span>
                  {selectedItem?.id === "all" ? (
                    <MdRadioButtonChecked className="text-teal-600" />
                  ) : (
                    <MdRadioButtonUnchecked className="text-teal-600" />
                  )}
                </div>

                {selectedSubcategory.items.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                    onClick={() => {
                      dispatch(setItem(item));
                      setOpen(false);
                    }}
                  >
                    <span>{item.name}</span>
                    {selectedItem?.id === item.id ? (
                      <MdRadioButtonChecked className="text-teal-600" />
                    ) : (
                      <MdRadioButtonUnchecked className="text-teal-600" />
                    )}
                  </div>
                ))}
              </>
            )}
          </div>
        )}
      </div>

      {/* {selectedItem && (
        <button className="border px-4 py-2 rounded-full bg-white shadow mt-3 flex items-center justify-between min-w-[100px]">
          <span>{selectedItem.name}</span>
          <FaTimes
            className="ml-2 cursor-pointer text-gray-600"
            onClick={(e) => {
              e.stopPropagation();
              reset();
            }}
            aria-label="Clear selection"
            size={16}
          />
        </button>
      )} */}
    </>
  );
}
