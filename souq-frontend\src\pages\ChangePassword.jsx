import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Alert } from '@mui/material';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Eye, EyeOff } from 'lucide-react';
import { resetPassword } from '../api/AuthService';

const ChangePassword = () => {
  const { register, handleSubmit, watch, formState: { errors, isSubmitting }, reset } = useForm();
  const [apiMessage, setApiMessage] = useState('');
  const [alertType, setAlertType] = useState('success');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token') || localStorage.getItem("accessToken"); // Get token from URL
  console.log(token, "token")
  const navigate = useNavigate()

  const onSubmit = async (data) => {
    setApiMessage('');
    setAlertType('');

    try {
      const response = await resetPassword(token, data); // pass token and form data

      if (response.success) {
        setApiMessage('Password changed successfully.');
        setAlertType('success');
        reset();
        navigate('/');
      } else {
        setApiMessage(response.error || 'Failed to reset password.');
        setAlertType('error');
      }
    } catch (err) {
      setApiMessage('Something went wrong. Please try again.');
      setAlertType('error');
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4">
      <div className="bg-white rounded-xl shadow-md p-8 max-w-md w-full mx-auto">
        <h1 className="text-2xl font-semibold mb-4 text-center">Change Password</h1>

        {apiMessage && (
          <Alert severity={alertType} className="mb-4">
            {apiMessage}
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
          {/* New Password */}
          <div className="relative">
            <input
              type={showNewPassword ? 'text' : 'password'}
              placeholder="New Password"
              className={`w-full border rounded-lg px-4 py-2 pr-10 focus:outline-none ${errors.newPassword ? 'border-red-500' : 'border-gray-300'
                }`}
              {...register('newPassword', {
                required: 'Password is required',
                minLength: {
                  value: 7,
                  message: 'Password must be at least 7 characters long',
                },
                pattern: {
                  value: /^(?=.*[A-Za-z])(?=.*\d).+$/,
                  message: 'Must contain at least 1 letter and 1 number',
                },
              })}
            />
            <span
              onClick={() => setShowNewPassword((prev) => !prev)}
              className="absolute right-3 top-2.5 cursor-pointer text-xl text-gray-600"
            >
              {showNewPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </span>
            {errors.newPassword && (
              <p className="text-red-500 text-sm mt-1">{errors.newPassword.message}</p>
            )}
          </div>

          {/* Confirm Password */}
          <div className="relative">
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="Confirm Password"
              className={`w-full border rounded-lg px-4 py-2 pr-10 focus:outline-none ${errors.reEnterNewPassword ? 'border-red-500' : 'border-gray-300'
                }`}
              {...register('reEnterNewPassword', {
                required: 'Please confirm your password',
                validate: (value) =>
                  value === watch('newPassword') || 'Passwords do not match',
              })}
            />
            <span
              onClick={() => setShowConfirmPassword((prev) => !prev)}
              className="absolute right-3 top-2.5 cursor-pointer text-xl text-gray-600"
            >
              {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </span>
            {errors.reEnterNewPassword && (
              <p className="text-red-500 text-sm mt-1">{errors.reEnterNewPassword.message}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-teal-600 hover:bg-teal-700 text-white py-2.5 rounded-lg font-semibold disabled:opacity-50"
          >
            {isSubmitting ? 'Updating...' : 'Change Password'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChangePassword;
