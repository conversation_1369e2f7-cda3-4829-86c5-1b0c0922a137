import React, { useEffect, useState } from 'react';
import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api/user';

// Get token from localStorage
const getToken = () => {
  return localStorage.getItem('authToken') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.9Z_OuDvWKivfnJQ6OM352Rdk5QkypiRoZdHkwY4xopc';
};

export default function ChatList({ onChatSelect, currentUserId }) {
  const [chats, setChats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  
  const token = getToken();

  useEffect(() => {
    fetchChats();
  }, []);

  const fetchChats = async (pageNum = 1) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(
        `${API_BASE_URL}/chat?page=${pageNum}&limit=20`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      const { chats: newChats, pagination } = response.data.data;
      
      if (pageNum === 1) {
        setChats(newChats);
      } else {
        setChats(prev => [...prev, ...newChats]);
      }
      
      setHasMore(pagination.hasNextPage);
      setPage(pageNum);
      setLoading(false);

    } catch (err) {
      console.error('Fetch chats error:', err);
      setError(err.response?.data?.message || 'Failed to fetch chats');
      setLoading(false);
    }
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      fetchChats(page + 1);
    }
  };

  const formatLastMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const truncateText = (text, maxLength = 50) => {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  if (loading && chats.length === 0) {
    return (
      <div style={{ padding: 20, textAlign: 'center' }}>
        <div>Loading chats...</div>
      </div>
    );
  }

  if (error && chats.length === 0) {
    return (
      <div style={{ padding: 20, textAlign: 'center', color: 'red' }}>
        <div>Error: {error}</div>
        <button onClick={() => fetchChats()} style={{ marginTop: 10, padding: '8px 16px' }}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div style={{ 
      height: '600px', 
      border: '1px solid #ddd',
      borderRadius: '8px',
      backgroundColor: '#fff',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{ 
        padding: '16px', 
        borderBottom: '1px solid #eee',
        backgroundColor: '#f8f9fa'
      }}>
        <h3 style={{ margin: 0, fontSize: '18px' }}>Messages</h3>
      </div>

      {/* Chat List */}
      <div style={{ 
        flex: 1, 
        overflowY: 'auto'
      }}>
        {chats.length === 0 ? (
          <div style={{ 
            padding: '40px 20px', 
            textAlign: 'center', 
            color: '#666' 
          }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>
            <div style={{ fontSize: '18px', marginBottom: '8px' }}>No messages yet</div>
            <div style={{ fontSize: '14px' }}>
              Start a conversation by messaging a seller about their product
            </div>
          </div>
        ) : (
          <>
            {chats.map((chat) => (
              <div
                key={chat.id}
                onClick={() => onChatSelect && onChatSelect(chat)}
                style={{
                  padding: '16px',
                  borderBottom: '1px solid #f0f0f0',
                  cursor: 'pointer',
                  backgroundColor: '#fff',
                  transition: 'background-color 0.2s',
                  ':hover': {
                    backgroundColor: '#f8f9fa'
                  }
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#fff'}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  {/* User Avatar */}
                  <div style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '50%',
                    backgroundColor: '#6c757d',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '18px',
                    fontWeight: 'bold',
                    flexShrink: 0
                  }}>
                    {chat.otherUser.firstName?.charAt(0) || 'U'}
                  </div>

                  {/* Chat Info */}
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      marginBottom: '4px'
                    }}>
                      <div style={{ 
                        fontWeight: 'bold', 
                        fontSize: '16px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {chat.otherUser.firstName} {chat.otherUser.lastName}
                      </div>
                      <div style={{ 
                        fontSize: '12px', 
                        color: '#666',
                        flexShrink: 0,
                        marginLeft: '8px'
                      }}>
                        {formatLastMessageTime(chat.lastMessageAt)}
                      </div>
                    </div>

                    <div style={{ 
                      fontSize: '14px', 
                      color: '#666',
                      marginBottom: '4px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      Product: {truncateText(chat.product.title, 30)}
                    </div>

                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center'
                    }}>
                      <div style={{ 
                        fontSize: '14px', 
                        color: chat.lastMessage ? '#333' : '#999',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        flex: 1
                      }}>
                        {chat.lastMessage ? (
                          <>
                            {chat.lastMessage.sender.id === currentUserId ? 'You: ' : ''}
                            {truncateText(chat.lastMessage.text)}
                          </>
                        ) : (
                          'No messages yet'
                        )}
                      </div>

                      {/* Unread Count Badge */}
                      {chat.unreadCount > 0 && (
                        <div style={{
                          backgroundColor: '#007bff',
                          color: 'white',
                          borderRadius: '12px',
                          padding: '2px 8px',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          minWidth: '20px',
                          textAlign: 'center',
                          marginLeft: '8px'
                        }}>
                          {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Load More Button */}
            {hasMore && (
              <div style={{ padding: '16px', textAlign: 'center' }}>
                <button
                  onClick={loadMore}
                  disabled={loading}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    opacity: loading ? 0.6 : 1
                  }}
                >
                  {loading ? 'Loading...' : 'Load More'}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
