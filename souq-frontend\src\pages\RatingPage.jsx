import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Star, ArrowLeft, CheckCircle, User, Package } from 'lucide-react';
import { toast } from 'react-toastify';
import LoadingSpinner from '../components/common/LoadingSpinner';
import RatingModal from '../components/Rating/RatingModal';
// import { canRateTransaction } from '../api/RatingService';
import { getEscrowTransaction, getTransactionDetails } from '../api/EscrowService';
import { getStandardPayment } from '../api/StandardPaymentService';


const RatingPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [transaction, setTransaction] = useState(null);
  const [canRate, setCanRate] = useState(false);
  const [ratingType, setRatingType] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [error, setError] = useState(null);

  const transactionId = searchParams.get('transaction');
  const type = searchParams.get('type') || 'escrow';
  // console.log('Type:', type);
  const baseURL = import.meta.env.VITE_API_BASE_URL?.replace(/\/api$/, '') || '';
  
  console.log('Transaction ID dataaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa:', transactionId);
  useEffect(() => {
    if (!transactionId) {
      setError('Transaction ID is required');
      setLoading(false);
      return;
    }

    loadTransactionAndCheckRating();
  }, [transactionId, type]);

  const loadTransactionAndCheckRating = async () => {
    try {
      setLoading(true);
      console.log(`🔍 Loading transaction: ${transactionId} (type: ${type})`);

      // Load transaction details
      let transactionData;
      let response;

      if (type === 'escrow') {
        console.log('📦 Loading escrow transaction...');
        try {
          response = await getEscrowTransaction(transactionId);
        } catch (primaryError) {
          console.log('Primary endpoint failed, trying alternative:', primaryError);
          response = await getTransactionDetails(transactionId);
        }
        console.log('📦 Escrow response:', response);

        if (response.success) {
          // Handle escrow transaction data structure
          const escrowData = response.data.escrowTransaction || response.data;
          transactionData = {
            ...escrowData,
            paymentType: 'escrow',
            id: escrowData._id || escrowData.id,
            escrowTransaction: escrowData
          };
        } else {
          console.error('❌ Failed to load escrow transaction:', response.message);
        }
      } else {
        console.log('💳 Loading standard payment...');
        response = await getStandardPayment(transactionId);
        console.log('💳 Standard payment response:', response);

        if (response.success) {
          // Handle standard payment data structure
          const paymentData = response.data.payment || response.data;
          transactionData = {
            ...paymentData,
            paymentType: 'standard',
            id: paymentData._id || paymentData.id
          };
        } else {
          console.error('❌ Failed to load standard payment:', response.message);
        }
      }

      if (!transactionData) {
        throw new Error(`${type === 'escrow' ? 'Escrow transaction' : 'Standard payment'} not found`);
      }

      console.log('✅ Transaction loaded:', transactionData);
      setTransaction(transactionData);

      // Check if user can rate this transaction
      console.log('🌟 Checking rating eligibility...');
      try {
        // const ratingResponse = await canRateTransaction(transactionId);
        console.log('🌟 Rating eligibility response:', ratingResponse);

        if (ratingResponse.success) {
          setCanRate(ratingResponse.data.canRate);
          setRatingType(ratingResponse.data.ratingType);
          setUserRole(ratingResponse.data.userRole);
          console.log(`🌟 Can rate: ${ratingResponse.data.canRate}, Type: ${ratingResponse.data.ratingType}, Role: ${ratingResponse.data.userRole}`);
        } else {
          console.warn('⚠️ Could not check rating eligibility:', ratingResponse.message);
          // Set default values if rating check fails
          setCanRate(true);
          setRatingType('buyer_to_seller');
          setUserRole('buyer');
        }
      } catch (ratingError) {
        console.warn('⚠️ Rating eligibility check failed:', ratingError);
        // Set default values if rating check fails
        setCanRate(true);
        setRatingType('buyer_to_seller');
        setUserRole('buyer');
      }

    } catch (error) {
      console.error('❌ Error loading transaction or rating data:', error);
      setError(error.message || 'Failed to load transaction details');
    } finally {
      setLoading(false);
    }
  };

  const handleRatingSubmitted = (ratingData) => {
    console.log('✅ Rating submitted:', ratingData);
    toast.success('Thank you for your rating! Your feedback helps build trust in our community.');
    setShowRatingModal(false);
    // setCanRate(false);
  };

  const getOtherUser = () => {
    if (!transaction || !userRole) return null;
    
    if (userRole === 'buyer') {
      return transaction.seller;
    } else {
      return transaction.buyer;
    }
  };

  const otherUser = getOtherUser();
  const isBuyerRating = ratingType === 'buyer_to_seller';

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Package className="w-10 h-10 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-3">Error Loading Transaction</h1>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">{error}</p>
            <button
              onClick={() => navigate('/')}
              className="bg-teal-600 text-white px-8 py-3 rounded-lg hover:bg-teal-700 transition-colors font-medium"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-6"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back
          </button>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Rate Your Transaction</h1>
          <p className="text-gray-600">Share your experience to help build trust in our community</p>
        </div>

        {/* Transaction Details Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
          <div className="p-6">
            <div className="flex items-start space-x-4">
              {/* Product Image or Icon */}
              <div className="flex-shrink-0">
                {transaction.product?.product_photos?.[0] ? (
                  <img
                    src={`${baseURL}/${transaction.product.product_photos[0]}`}
                    alt={transaction.product.title}
                    className="w-16 h-16 object-cover rounded-lg"
                  />
                ) : (
                  <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Package className="w-8 h-8 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Transaction Info */}
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {transaction.product?.title || 'Product'}
                </h3>
                <div className="space-y-1 text-sm text-gray-600">
                  <p><span className="font-medium">Transaction ID:</span> {transaction.transactionId || 'N/A'}</p>
                  <p><span className="font-medium">Amount:</span> ${transaction.productPrice || transaction.amount || 0}</p>
                  <p><span className="font-medium">Type:</span> {type === 'escrow' ? 'Escrow Payment' : 'Standard Payment'}</p>
                  <p><span className="font-medium">Date:</span> {transaction.createdAt ? new Date(transaction.createdAt).toLocaleDateString() : 'Invalid Date'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Rating Section */}
        { otherUser ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="p-8">
              <div className="text-center mb-8">
                <div className="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Star className="w-10 h-10 text-yellow-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-3">
                  {isBuyerRating ? 'Rate Your Seller' : 'Rate Your Buyer'}
                </h2>
                <p className="text-gray-600 max-w-md mx-auto">
                  How was your experience with {otherUser.firstName} {otherUser.lastName}?
                </p>
              </div>

              {/* User Info */}
              <div className="flex items-center justify-center space-x-4 mb-8">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                  {otherUser.profile ? (
                    <img
                      src={`${baseURL}${otherUser.profile}`}
                      alt={`${otherUser.firstName} ${otherUser.lastName}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="w-8 h-8 text-gray-400" />
                  )}
                </div>
                <div className="text-center">
                  <p className="font-semibold text-gray-900 text-lg">
                    {otherUser.firstName} {otherUser.lastName}
                  </p>
                  <p className="text-sm text-gray-600">
                    {isBuyerRating ? 'Seller' : 'Buyer'}
                  </p>
                </div>
              </div>

              {/* Rate Button */}
              <div className="text-center">
                <button
                  onClick={() => setShowRatingModal(true)}
                  className="bg-teal-600 text-white px-8 py-3 rounded-lg hover:bg-teal-700 transition-colors flex items-center space-x-2 mx-auto font-medium"
                >
                  <Star className="w-5 h-5" />
                  <span>Rate Now</span>
                </button>
              </div>
            </div>
          </div>

        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="p-8 text-center">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-10 h-10 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-3">
                {canRate === false ? 'Rating Already Submitted' : 'Rating Not Available'}
              </h2>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                {canRate === false
                  ? 'You have already rated this transaction. Thank you for your feedback!'
                  : 'Rating is not available for this transaction at this time.'
                }
              </p>
              <button
                onClick={() => navigate('/dashboard')}
                className="bg-teal-600 text-white px-8 py-3 rounded-lg hover:bg-teal-700 transition-colors font-medium"
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        )}

        {/* Rating Modal */}
        <RatingModal
          isOpen={showRatingModal}
          onClose={() => setShowRatingModal(false)}
          transaction={transaction}
          ratingType={ratingType}
          ratedUser={otherUser}
          onRatingSubmitted={handleRatingSubmitted}
        />
      </div>
    </div>
  );
};

export default RatingPage;
