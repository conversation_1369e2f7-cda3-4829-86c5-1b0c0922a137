import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Header from './components/Header/Header';
import Footer from './components/Footer/Footer';
import HomePage from './pages/HomePage';
import { AppProvider } from './context/AppContext';
import { NetworkErrorProvider } from './context/NetworkErrorContext';
import ProductDetails from './pages/ProductDetails';
import Profile from './pages/Profile';
import ScrollToTop from './components/ScrollToTop';
import ChangePassword from './pages/ChangePassword';
import EmailVerification from './pages/EmailVerfication';
import NotReceiveMail from './pages/NotReceiveMail';
import SendPhoneOTP from './pages/SendPhoneOtp';
import PhoneVeriy from './pages/PhoneVerify';
import MemberProfile from './pages/MemberProfile';
import Settings from './pages/Settings';
import ProtectedRoute from './route/ProtectedRoute';
import AuthCallback from './pages/AuthPage';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import SellNowPage from './pages/SellNow';
import LoginActivity from './pages/LoginActivity';
import EmailChange from './pages/EmailChange';
import FavoritesItem from './pages/FavoritesItem';
import FollowingPage from './pages/Following';
import Followers from './pages/Followers';
import CheckOut from './pages/Checkout';
import EscrowCheckout from './pages/EscrowCheckout';
import EscrowTransaction from './pages/EscrowTransaction';
import PaymentSuccess from './pages/PaymentSuccess';
import PaymentCancelled from './pages/PaymentCancelled';
import StripePayment from './pages/StripePayment';
import DisabledStripePayment from './components/Payment/DisabledStripePayment';
import PaymentMethods from './pages/PaymentMethods';
import ChatLayout from './pages/ChatLayout';
import ChatRoom from './components/Chat/ChatRoom';
import ChatTest from './components/Chat/ChatTest';
import Orders from './pages/Orders';
import OrderDetails from './pages/OrderDetails';
import RatingTest from './components/Rating/RatingTest';
import RatingPage from './pages/RatingPage';
import RatingNotification from './components/Rating/RatingNotification';
import RatingTestButton from './components/Rating/RatingTestButton';
import Wallet from './pages/Wallet';
import LocationTest from './pages/LocationTest';
import { isPaymentGatewayEnabled } from './config/paymentConfig';

function App() {
  // Determine which Stripe component to use based on configuration
  const StripeComponent = isPaymentGatewayEnabled('stripe') ? StripePayment : DisabledStripePayment;

  return (
    <AppProvider>
      <NetworkErrorProvider>
        <ScrollToTop />
        <div className="min-h-screen flex flex-col bg-gray-50">
        <Header />
        <ToastContainer />
        <RatingNotification />
        <RatingTestButton />
        <main className="flex-grow bg-white">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/auth-callback" element={<AuthCallback />} />
            <Route path="/product-details/:id" element={<ProductDetails />} />
            <Route path="/profile/:id" element={<Profile />} />
            <Route path="/email-verify" element={<EmailVerification />} />
            <Route path="/email-not-receive" element={<NotReceiveMail />} />
            <Route path="/send-phone-otp" element={<SendPhoneOTP />} />
            <Route path="/phone-verify" element={<PhoneVeriy />} />
            <Route path="/reset-password" element={<ChangePassword />} />
            <Route path="/following/:id" element={<FollowingPage />} />
            <Route path="/followers/:id" element={<Followers />} />
            {/* <Route path="/chat-layout" element={<ChatLayout />} /> */}
            <Route
              path="/member-profile"
              element={
                <ProtectedRoute>
                  <MemberProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/settings"
              element={
                <ProtectedRoute>
                  <Settings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/wallet"
              element={
                <ProtectedRoute>
                  <Wallet />
                </ProtectedRoute>
              }
            />
            <Route
              path="/sell-now"
              element={
                <ProtectedRoute>
                  <SellNowPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/favorites-item"
              element={
                <ProtectedRoute>
                  <FavoritesItem />
                </ProtectedRoute>
              }
            />
            <Route
              path="/checkout"
              element={
                <ProtectedRoute>
                  <CheckOut />
                </ProtectedRoute>
              }
            />
            <Route
              path="/escrow-checkout"
              element={
                <ProtectedRoute>
                  <EscrowCheckout />
                </ProtectedRoute>
              }
            />
            <Route
              path="/escrow/transaction/:transactionId"
              element={
                <ProtectedRoute>
                  <EscrowTransaction />
                </ProtectedRoute>
              }
            />
            <Route
              path="/escrow/payment-success"
              element={
                <ProtectedRoute>
                  <PaymentSuccess />
                </ProtectedRoute>
              }
            />
            <Route
              path="/payment-success"
              element={
                <ProtectedRoute>
                  <PaymentSuccess />
                </ProtectedRoute>
              }
            />
            <Route
              path="/escrow/stripe-payment"
              element={
                <ProtectedRoute>
                  <StripeComponent />
                </ProtectedRoute>
              }
            />
            <Route
              path="/stripe-payment"
              element={
                <ProtectedRoute>
                  <StripeComponent />
                </ProtectedRoute>
              }
            />
            <Route
              path="/escrow/payment-cancelled"
              element={
                <ProtectedRoute>
                  <PaymentCancelled />
                </ProtectedRoute>
              }
            />
            <Route
              path="/payment-cancelled"
              element={
                <ProtectedRoute>
                  <PaymentCancelled />
                </ProtectedRoute>
              }
            />
            <Route
              path="/payment-methods"
              element={
                <ProtectedRoute>
                  <PaymentMethods />
                </ProtectedRoute>
              }
            />
            <Route
              path="/orders"
              element={
                <ProtectedRoute>
                  <Orders />
                </ProtectedRoute>
              }
            />
            <Route
              path="/order/:orderId"
              element={
                <ProtectedRoute>
                  <OrderDetails />
                </ProtectedRoute>
              }
            />

             <Route
              path="/chat-layout"
              element={
                <ProtectedRoute>
                  <ChatLayout />
                </ProtectedRoute>
              }
            />
            <Route
              path="/chat-test"
              element={
                <ProtectedRoute>
                  <ChatTest />
                </ProtectedRoute>
              }
            />
            <Route
              path="/chat/:productId"
              element={
                <ProtectedRoute>
                  <ChatRoom />
                </ProtectedRoute>
              }
            />
            <Route
              path="/chat/:productId/:roomId"
              element={
                <ProtectedRoute>
                  <ChatRoom />
                </ProtectedRoute>
              }
            />
            <Route
              path="/login-activity"
              element={
                <ProtectedRoute>
                  <LoginActivity />
                </ProtectedRoute>
              }
            />
            <Route
              path="/email-change"
              element={
                <ProtectedRoute>
                  <EmailChange />
                </ProtectedRoute>
              }
            />
            <Route
              path="/rating-test"
              element={
                <ProtectedRoute>
                  <RatingTest />
                </ProtectedRoute>
              }
            />
            <Route
              path="/rating"
              element={
                <ProtectedRoute>
                  <RatingPage />
                </ProtectedRoute>
              }
            />
            <Route
              // path="/location-test"
              // element={<LocationTest />}
            />
          </Routes>

        </main>
        <Footer />
        </div>
      </NetworkErrorProvider>
    </AppProvider>
  );
}

export default App;
