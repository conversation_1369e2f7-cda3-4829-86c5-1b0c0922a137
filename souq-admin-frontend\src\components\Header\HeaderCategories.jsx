import React from 'react';
import { useAppContext } from '../../context/AppContext';
import { categories } from '../../data/categories';

const HeaderCategories = () => {
  const { 
    setIsMegaMenuOpen, 
    activeCategory, 
    setActiveCategory 
  } = useAppContext();

  const handleCategoryHover = (categoryId) => {
    setActiveCategory(categoryId);
    setIsMegaMenuOpen(true);
  };

  return (
    <nav className="flex items-center h-full" aria-label="Product categories">
      <ul className="flex space-x-6" role="menubar">
        {categories.map((category) => (
          <li 
            key={category.id}
            className="relative group h-full flex items-center"
            onMouseEnter={() => handleCategoryHover(category.id)}
            role="none"
          >
            <button 
              className={`text-sm font-medium transition-colors duration-200 py-1 ${
                activeCategory === category.id 
                  ? 'text-teal-600 border-b-2 border-teal-600' 
                  : 'text-gray-700 hover:text-teal-600'
              }`}
              role="menuitem"
              aria-current={activeCategory === category.id ? 'page' : undefined}
              onFocus={() => handleCategoryHover(category.id)}  // Keyboard accessibility
            >
              {category.name}
            </button>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default HeaderCategories;
