import React from 'react';
import { Facebook, Instagram, Twitter } from 'lucide-react';
import Logo from '../common/Logo';

const Footer = () => {
  return (
    <footer className="bg-white border-t border-gray-100 pt-10 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <Logo />
            <p className="mt-4 text-gray-600 text-sm">
              The go-to marketplace for buying and selling secondhand fashion.
            </p>
            <div className="flex space-x-4 mt-6">
              <a href="#" className="text-gray-500 hover:text-teal-500">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-500 hover:text-teal-500">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-500 hover:text-teal-500">
                <Twitter size={20} />
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-800 mb-4">Company</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">About</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Careers</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Press</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Sustainability</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-800 mb-4">Support</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Help Center</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Safety Center</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Community Guidelines</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Contact Us</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-800 mb-4">Legal</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Privacy Policy</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Terms of Service</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Cookie Policy</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">Accessibility</a></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-100 mt-8 pt-6">
          <p className="text-center text-gray-500 text-xs">
            © {new Date().getFullYear()} SOUQ All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
