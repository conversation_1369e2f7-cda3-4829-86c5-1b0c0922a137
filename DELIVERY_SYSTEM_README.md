# SOUQ Delivery System

A comprehensive delivery and shipping management system for the SOUQ marketplace, supporting multiple shipping providers, local pickup/drop-off options, and real-time tracking.

## Features

### 🚚 Multiple Shipping Providers
- **Aramex**: Express and ground delivery services across the Middle East
- **Fetchr**: Local and regional delivery with same-day options
- **DHL Express**: International and premium delivery services
- **Local Pickup**: Direct pickup from seller locations
- **Local Drop-off**: Convenient drop-off points for buyers

### 📦 Order Management
- Complete order lifecycle tracking
- Real-time status updates
- Buyer and seller dashboards
- Order history and statistics
- Delivery confirmation system

### 🗺️ Local Delivery Options
- Pickup location management
- Drop-off point configuration
- Operating hours scheduling
- Contact information management
- Geographic coordination support

### 📱 Real-time Tracking
- Live shipment tracking
- Delivery progress visualization
- Push notifications for status updates
- Estimated delivery times
- Delivery confirmation

## Backend Architecture

### Database Models

#### ShippingProvider
```javascript
{
  name: String, // 'aramex', 'fetchr', 'dhl', 'local_pickup', 'local_dropoff'
  displayName: String,
  isActive: Boolean,
  configuration: {
    // Provider-specific API credentials
  },
  supportedServices: [{
    serviceCode: String,
    serviceName: String,
    estimatedDays: { min: Number, max: Number }
  }],
  pricing: {
    baseFee: Number,
    perKgRate: Number,
    currency: String
  }
}
```

#### Order
```javascript
{
  orderNumber: String,
  buyer: ObjectId,
  seller: ObjectId,
  product: ObjectId,
  status: String, // 'pending_payment', 'paid', 'shipped', 'delivered', etc.
  shipping: {
    provider: ObjectId,
    trackingNumber: String,
    estimatedDelivery: Date,
    toAddress: Object
  },
  timeline: [{
    status: String,
    timestamp: Date,
    description: String,
    updatedBy: String
  }]
}
```

#### Shipment
```javascript
{
  order: ObjectId,
  shippingProvider: ObjectId,
  trackingNumber: String,
  tracking: {
    status: String,
    events: [{
      timestamp: Date,
      status: String,
      description: String,
      location: Object
    }]
  }
}
```

### API Endpoints

#### Shipping Management
- `GET /api/user/shipping/providers` - Get available shipping providers
- `POST /api/user/shipping/rates` - Calculate shipping rates
- `POST /api/user/shipping/shipments` - Create shipment
- `GET /api/user/shipping/track/:trackingNumber` - Track shipment

#### Delivery Options
- `GET /api/user/shipping/delivery-options` - Get user delivery preferences
- `POST /api/user/shipping/delivery-options` - Save delivery option
- `PUT /api/user/shipping/delivery-options/:id/default` - Set default option

#### Order Management
- `GET /api/user/orders` - Get user orders
- `GET /api/user/orders/:id` - Get order details
- `POST /api/user/orders` - Create order
- `PUT /api/user/orders/:id/status` - Update order status
- `POST /api/user/orders/:id/confirm-delivery` - Confirm delivery

### Service Classes

#### BaseShippingService
Abstract base class defining the common interface for all shipping providers:
- `getShippingRates(origin, destination, packageDetails)`
- `createShipment(shipmentData)`
- `trackShipment(trackingNumber)`
- `cancelShipment(shipmentId)`

#### Provider-Specific Services
- **AramexService**: Integrates with Aramex API
- **FetchrService**: Integrates with Fetchr API
- **DHLService**: Integrates with DHL Express API
- **LocalDeliveryService**: Handles local pickup/drop-off

#### ShippingServiceFactory
Manages all shipping service instances and provides:
- Service initialization and health checks
- Rate comparison across providers
- Automatic provider selection
- Error handling and fallbacks

## Frontend Components

### Pages
- **Orders**: Order listing and management
- **OrderDetails**: Individual order tracking and details

### Components
- **DeliverySettings**: Shipping provider and local delivery configuration
- **LocalDeliverySettings**: Pickup/drop-off location management
- **TrackingWidget**: Real-time shipment tracking display
- **DeliveryProgress**: Visual progress indicator
- **DeliveryNotifications**: Push notification system

### Services
- **ShippingService**: API communication layer
- **Currency formatting and status mapping utilities**

## Setup Instructions

### Backend Setup

1. **Install Dependencies**
   ```bash
   cd souq-backend
   npm install
   ```

2. **Environment Variables**
   Create a `.env` file with:
   ```env
   # Aramex Configuration
   ARAMEX_USERNAME=your_username
   ARAMEX_PASSWORD=your_password
   ARAMEX_ACCOUNT_NUMBER=your_account_number
   ARAMEX_ACCOUNT_PIN=your_pin
   ARAMEX_ACCOUNT_ENTITY=DXB
   ARAMEX_ACCOUNT_COUNTRY_CODE=AE
   ARAMEX_ENVIRONMENT=sandbox

   # Fetchr Configuration
   FETCHR_API_KEY=your_api_key
   FETCHR_SECRET_KEY=your_secret_key
   FETCHR_ENVIRONMENT=sandbox

   # DHL Configuration
   DHL_API_KEY=your_api_key
   DHL_SECRET_KEY=your_secret_key
   DHL_ACCOUNT_NUMBER=your_account_number
   DHL_ENVIRONMENT=sandbox
   ```

3. **Initialize Shipping Providers**
   ```bash
   npm run init-shipping
   ```

4. **Start the Server**
   ```bash
   npm run dev
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd souq-frontend
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm start
   ```

## Usage Guide

### For Sellers

1. **Configure Delivery Options**
   - Go to Settings > Delivery
   - Add preferred shipping providers
   - Set up local pickup locations
   - Configure operating hours

2. **Process Orders**
   - View orders in Orders page
   - Mark orders as processing when ready
   - Create shipments with tracking numbers
   - Update order status as needed

### For Buyers

1. **Place Orders**
   - Select shipping address during checkout
   - Choose from available delivery options
   - View shipping costs and estimated delivery

2. **Track Orders**
   - Monitor order progress in Orders page
   - Receive notifications for status updates
   - Confirm delivery when received
   - Rate delivery experience

### For Administrators

1. **Manage Providers**
   - Configure shipping provider settings
   - Monitor provider performance
   - Update pricing and service options

2. **System Monitoring**
   - Track delivery success rates
   - Monitor API health status
   - Analyze delivery performance metrics

## API Integration Examples

### Calculate Shipping Rates
```javascript
const rates = await ShippingService.getShippingRates(
  origin,
  destination,
  {
    weight: 1,
    dimensions: { length: 20, width: 15, height: 10 },
    value: 100,
    currency: 'USD'
  }
);
```

### Create Shipment
```javascript
const shipment = await ShippingService.createShipment(
  orderId,
  'aramex',
  'PPX',
  {
    origin: sellerAddress,
    destination: buyerAddress,
    packages: [packageDetails]
  }
);
```

### Track Shipment
```javascript
const tracking = await ShippingService.trackShipment(trackingNumber);
```

## Testing

### Provider Testing
Each shipping provider service includes test methods for:
- Rate calculation validation
- Shipment creation testing
- Tracking functionality verification
- Error handling scenarios

### Integration Testing
- End-to-end order flow testing
- Multi-provider rate comparison
- Local delivery workflow testing
- Notification system verification

## Troubleshooting

### Common Issues

1. **Provider API Errors**
   - Check API credentials in environment variables
   - Verify provider account status
   - Review API rate limits

2. **Tracking Issues**
   - Ensure tracking numbers are valid
   - Check provider service status
   - Verify network connectivity

3. **Local Delivery Problems**
   - Confirm pickup/drop-off locations are active
   - Check operating hours configuration
   - Verify address formatting

### Monitoring

The system includes built-in monitoring for:
- Provider API health checks
- Delivery success rates
- Average delivery times
- Error tracking and reporting

## Future Enhancements

- **AI-powered delivery optimization**
- **Drone delivery integration**
- **Carbon footprint tracking**
- **Advanced analytics dashboard**
- **Multi-language support**
- **Mobile app integration**

## Support

For technical support or questions about the delivery system:
- Check the troubleshooting guide above
- Review API documentation
- Contact the development team

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Compatibility**: Node.js 16+, React 18+
