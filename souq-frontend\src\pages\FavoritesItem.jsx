import React, { useEffect, useState } from 'react';
import { getFevItems } from '../api/ProductService';
import ProductGrid from '../components/Products/ProductGrid';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { useNavigate } from 'react-router-dom';

const FavoritesItem = () => {
    const [favoriteProducts, setFavoriteProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [apiRefresh, setApiRefresh] = useState("")
    const navigate = useNavigate()
    useEffect(() => {
        getFevItems()
            .then((res) => {
                console.log(res, "Favorites response");
                setFavoriteProducts(res?.data?.data?.favorites || []);
            })
            .catch((err) => {
                console.log(err, "Error fetching favorites");
            })
            .finally(() => {
                setLoading(false);
            });
    }, [apiRefresh]);

    return (
        <div className='container mx-auto py-8'>
            <h1 className="text-2xl font-semibold mb-4">Favorited items</h1>
            {loading ? (
                <LoadingSpinner fullScreen />
            ) : favoriteProducts.length === 0 ? (
                <div className="h-[70vh] flex flex-col justify-center items-center text-center space-y-4">
                    <p className="text-lg font-medium text-gray-500">No favorite items found.</p>
                    <button
                        onClick={() => navigate('/')}
                        className="px-5 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition"
                    >
                        Browse
                    </button>
                </div>
            ) : (
                <ProductGrid products={favoriteProducts} apiRefresh={apiRefresh} setApiRefresh={setApiRefresh} />
            )}

        </div>
    );
};

export default FavoritesItem;
