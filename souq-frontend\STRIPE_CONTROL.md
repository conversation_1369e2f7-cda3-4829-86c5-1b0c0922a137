# Stripe Payment Control

This document explains how to control Stripe payment functionality in the SOUQ frontend application.

## Problem Solved

The continuous API calls to `https://m.stripe.com/6` have been resolved by implementing proper memoization and configuration controls.

## Configuration

### Payment Configuration File
Location: `src/config/paymentConfig.js`

```javascript
const paymentConfig = {
  stripe: {
    enabled: false, // Set to true to enable Stripe payments
    preventApiCalls: true, // Set to false to allow Stripe API calls
    reason: 'Disabled to prevent continuous API calls to https://m.stripe.com/6'
  }
};
```

## How to Control Stripe Functionality

### 1. Completely Disable Stripe (Current State)
- `enabled: false` - Uses DisabledStripePayment component
- `preventApiCalls: true` - Prevents any Stripe API calls
- Users see a disabled payment page with explanation

### 2. Enable Stripe with API Call Prevention
- `enabled: true` - Uses StripePayment component
- `preventApiCalls: true` - Prevents loadStripe() calls
- Users see loading state but no actual Stripe functionality

### 3. Fully Enable Stripe (Normal Operation)
- `enabled: true` - Uses StripePayment component
- `preventApiCalls: false` - Allows all Stripe API calls
- Full Stripe payment functionality restored

## Components

### DisabledStripePayment
- Location: `src/components/Payment/DisabledStripePayment.jsx`
- Shows user-friendly disabled message
- Prevents all Stripe API calls
- Provides navigation back to checkout

### StripePayment (Enhanced)
- Location: `src/pages/StripePayment.jsx`
- Enhanced with configuration checks
- Proper memoization to prevent continuous API calls
- Cleanup mechanisms to prevent memory leaks

## Changes Made

1. **Memoized Stripe Promise**: Prevents continuous `loadStripe()` calls
2. **Configuration System**: Easy enable/disable control
3. **Component Cleanup**: Prevents state updates on unmounted components
4. **Route Management**: Added missing routes for standard payments
5. **Disabled Component**: User-friendly alternative when Stripe is disabled

## To Re-enable Stripe

1. Open `src/config/paymentConfig.js`
2. Set `stripe.enabled: true`
3. Set `stripe.preventApiCalls: false`
4. Restart the development server

## Testing

- With current settings, no Stripe API calls will be made
- Users will see a clear disabled message
- Navigation remains functional
- No continuous network requests to stripe.com

## Notes

- The configuration is checked at runtime
- Changes require application restart
- All other payment gateways remain unaffected
- The system is designed to be easily reversible
