const nodemailer = require('nodemailer');

// Check if email is disabled for development
const isEmailDisabled = process.env.DISABLE_EMAIL === 'true' || process.env.NODE_ENV === 'development';

let transporter = null;

// Only create transporter if email is enabled and credentials are available
if (!isEmailDisabled && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
  try {
    transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
    console.log('✅ Email transporter initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize email transporter:', error.message);
  }
} else {
  console.log('⚠️ Email service disabled or credentials missing');
  console.log('   - EMAIL_USER:', process.env.EMAIL_USER ? 'Set' : 'Missing');
  console.log('   - EMAIL_PASS:', process.env.EMAIL_PASS ? 'Set' : 'Missing');
  console.log('   - DISABLE_EMAIL:', process.env.DISABLE_EMAIL);
  console.log('   - NODE_ENV:', process.env.NODE_ENV);
}

const sendMail = async (to, subject, html) => {
  try {
    console.log(`📧 Attempting to send email to: ${to}`);
    console.log(`📧 Subject: ${subject}`);
    console.log(`📧 Email service status check:`);
    console.log(`   - isEmailDisabled: ${isEmailDisabled}`);
    console.log(`   - transporter exists: ${!!transporter}`);
    console.log(`   - DISABLE_EMAIL: ${process.env.DISABLE_EMAIL}`);
    console.log(`   - NODE_ENV: ${process.env.NODE_ENV}`);
    console.log(`   - EMAIL_USER: ${process.env.EMAIL_USER ? 'Set' : 'Missing'}`);
    console.log(`   - EMAIL_PASS: ${process.env.EMAIL_PASS ? 'Set' : 'Missing'}`);

    // If email is disabled or no transporter, simulate sending
    if (isEmailDisabled || !transporter) {
      console.log('⚠️ Email sending is disabled - simulating email send');
      console.log('📧 Reason for simulation:');
      if (isEmailDisabled) {
        console.log('   - Email is disabled via environment variables');
      }
      if (!transporter) {
        console.log('   - Email transporter failed to initialize');
      }
      console.log('📧 Email content (would be sent):');
      console.log(`   To: ${to}`);
      console.log(`   Subject: ${subject}`);
      console.log(`   HTML: ${html}`);

      // Return a mock successful response
      return {
        messageId: 'mock-message-id',
        response: 'Email sending disabled - simulated success',
        simulated: true
      };
    }

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to,
      subject,
      html
    };

    console.log('📧 Sending email via Gmail...');
    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent successfully:', result.messageId);

    return result;

  } catch (err) {
    console.error("❌ Email sending failed:", err);
    console.error("❌ Error details:", {
      code: err.code,
      command: err.command,
      response: err.response,
      responseCode: err.responseCode
    });

    // Provide more specific error messages
    if (err.code === 'EAUTH') {
      throw new Error("Email authentication failed - check EMAIL_USER and EMAIL_PASS");
    } else if (err.code === 'ECONNECTION') {
      throw new Error("Email connection failed - check internet connection");
    } else if (err.code === 'EMESSAGE') {
      throw new Error("Email message format error");
    } else {
      throw new Error(`Email failed to send: ${err.message}`);
    }
  }
};

// Function to force enable email for testing
const forceEnableEmail = () => {
  if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
    console.error('❌ Cannot force enable email - EMAIL_USER or EMAIL_PASS missing');
    return false;
  }

  try {
    const nodemailer = require('nodemailer');
    const newTransporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    // Replace the global transporter
    transporter = newTransporter;
    console.log('✅ Email forcefully enabled for testing');
    return true;
  } catch (error) {
    console.error('❌ Failed to force enable email:', error.message);
    return false;
  }
};

// Function to test email configuration
const testEmailConfig = async () => {
  try {
    console.log('🧪 Testing email configuration...');

    if (!transporter) {
      console.log('❌ No transporter available');
      return false;
    }

    // Verify the transporter
    const verified = await transporter.verify();
    console.log('✅ Email configuration verified:', verified);
    return verified;

  } catch (error) {
    console.error('❌ Email configuration test failed:', error.message);
    return false;
  }
};

module.exports = {
  sendMail,
  forceEnableEmail,
  testEmailConfig
};

// For backward compatibility, also export sendMail as default
module.exports = sendMail;
module.exports.forceEnableEmail = forceEnableEmail;
module.exports.testEmailConfig = testEmailConfig;