import React, { useEffect, useRef, useState } from 'react';
import { Search, Menu, X, ChevronDown, ChevronUp } from 'lucide-react';
import { useAppContext } from '../../context/AppContext';
import MegaMenu from './MegaMenu';
import HeaderCategories from './HeaderCategories';
import Logo from '../common/Logo';
import MobileMenu from './MobileMenu';
import {
  LuHeart,
  LuUser,
  LuSettings,
  LuWallet,
  LuPackage,
  LuGift,
  LuLogOut,
  LuUsers,
  LuSparkles,
  LuMail,
} from "react-icons/lu";
import { useNavigate } from 'react-router-dom';
import { logout } from '../../api/AuthService';
import { clearTokens } from '../../utils/TokenStorage';
import { useSelector } from 'react-redux';
import DeliveryNotifications from '../Tracking/DeliveryNotifications';


const Header = () => {
  const {
    isAuthModalOpen,
    setIsAuthModalOpen,
    setAuthMode,
    isMobileMenuOpen,
    setIsMobileMenuOpen,
    setActiveCategory
  } = useAppContext();

  const [isScrolled, setIsScrolled] = useState(false);
  const [isMegaMenuOpen, setIsMegaMenuOpen] = useState(false);
  const [isCatalogOpen, setIsCatalogOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState('Catalog');
  const [profileOpen, setProfileOpen] = useState(false);
  const token = localStorage.getItem("user")
  const navigate = useNavigate()
  const profileImage = useSelector((state) => state.profile.profileImage);
  const [isOpen, setIsOpen] = useState(false);
  const [language, setLanguage] = useState("en");
  const dropdownRef = useRef(null);
console.log("profileImage", profileImage);

  useEffect(() => {
    const savedLang = localStorage.getItem("lang") || "en";
    setLanguage(savedLang);
    document.documentElement.lang = savedLang;
    document.documentElement.dir = savedLang === "ar" ? "rtl" : "ltr";
  }, []);

  const handleLanguageChange = (lang) => {
    setLanguage(lang);
    localStorage.setItem("lang", lang);
    document.documentElement.lang = lang;
    document.documentElement.dir = lang === "ar" ? "rtl" : "ltr";
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const ref = useRef();
  const profileRef = useRef(null);

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileRef.current && !profileRef.current.contains(event.target)) {
        setProfileOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        setIsCatalogOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleCatalog = () => {
    setIsCatalogOpen(!isCatalogOpen);
  };

  const handleLogin = () => {
    setAuthMode('login');
    setIsAuthModalOpen(true);
  };

  const isAuthenticated = localStorage.getItem("user");

  const handleSellNow = () => {
    if (isAuthenticated) {
      navigate("/sell-now")
    } else {
      setAuthMode('login');
      setIsAuthModalOpen(true);
    }
  };

  const handleSignup = () => {
    setAuthMode('signup');
    setIsAuthModalOpen(true);
  };

  const handleLogout = async () => {
    try {
      await logout();
      clearTokens();
      navigate("/");
      window.location.reload();
    } catch (error) {
      clearTokens();
      navigate("/");
      window.location.reload();
    }
  };

  return (
    <header className={`sticky top-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white shadow-sm' : 'bg-white'}`}>
      <div className="container mx-auto px-4 flex items-center justify-between h-16 md:h-20">
        {/* Logo */}
        <div className="flex items-center">
          <Logo />
        </div>

        {/* Search Bar and Catalog Dropdown */}
        <div className="hidden lg:flex items-center flex-grow mx-8">
          <div ref={ref} className="relative flex items-center bg-gray-100 w-full h-12 rounded-lg">
            <div
              className={`px-4 py-2 cursor-pointer flex items-center whitespace-nowrap overflow-hidden text-ellipsis`}
              onClick={toggleCatalog}
            >
              {selectedOption}
              <span className="ml-2">
                {isCatalogOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </span>
            </div>

            {isCatalogOpen && (
              <div className="absolute bg-white border rounded-lg shadow-lg py-2 z-50 min-w-[12rem] max-w-md top-12">
                {['Catalog', 'Members', 'Help Center'].map(option => (
                  <div
                    key={option}
                    onClick={() => {
                      setSelectedOption(option);
                      setIsCatalogOpen(false);
                    }}
                    title={option}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer whitespace-nowrap text-ellipsis overflow-hidden"
                  >
                    {option}
                  </div>
                ))}
              </div>
            )}

            {/* Divider */}
            <div className="h-full w-px bg-gray-300 mx-2"></div>
            <div className="flex items-center w-full">
              <Search className="text-gray-400 ml-3 mr-2" size={18} />
              <input
                type="text"
                placeholder="Search for items"
                className="bg-transparent focus:outline-none w-full h-full rounded-lg p-2"
              />
            </div>
          </div>
        </div>

        {/* Auth and Sell Buttons */}
        <div className="hidden lg:flex items-center space-x-4">
          <>
            {token ? (
              <div className="flex items-center gap-4 relative" ref={profileRef}>
                <LuMail className="w-5 h-5 text-gray-600 cursor-pointer" onClick={() => navigate("/chat-layout")} />
                <DeliveryNotifications />
                <LuHeart className="w-5 h-5 text-gray-600 cursor-pointer" onClick={() => navigate("/favorites-item")} />

                {/* Profile Picture */}
                <div className="relative">
                  <img
                    src={profileImage ? profileImage : "https://cdn-icons-png.flaticon.com/512/149/149071.png"}
                    alt="Profile"
                    onClick={() => setProfileOpen(!profileOpen)}
                    className="w-8 h-8 rounded-full cursor-pointer border border-gray-300 rtl:ml-4"
                  />

                  {/* Dropdown */}
                  {profileOpen && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border rounded-lg shadow-lg z-50">
                      <ul className="text-gray-500">
                        <li className="px-4 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer" onClick={() => { navigate("/member-profile"); setProfileOpen(false) }}>
                          <LuUser /> Profile
                        </li>
                        <li className="px-4 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer" onClick={() => { navigate("/settings"); setProfileOpen(false) }}>
                          <LuSettings /> Settings
                        </li>
                        <li className="px-4 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer">
                          <LuSparkles /> Personalization
                        </li>
                        <li className="px-4 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer" onClick={() => { navigate("/wallet"); setProfileOpen(false) }}>
                          <LuWallet /> Wallet
                        </li>
                        <li className="px-4 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer" onClick={() => { navigate("/orders"); }} >
                          <LuPackage /> My Orders
                        </li>
                        <li className="px-4 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer">
                          <LuGift /> Donation
                        </li>
                        <li className="px-4 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer">
                          <LuUsers /> Invite Friends
                        </li>
                        <li
                          onClick={handleLogout}
                          className="px-4 py-2 hover:bg-red-50 text-red-500 flex items-center gap-2 cursor-pointer border-t"
                        >
                          <LuLogOut /> Logout
                        </li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="border border-teal-500 flex items-center rounded-lg rtl:ml-4">
                <button
                  onClick={handleSignup}
                  className="text-teal-500 px-4 py-2 hover:bg-teal-50 rounded-lg"
                >
                  Sign up
                </button>
                <div className="h-6 w-px bg-teal-500" />
                <button
                  onClick={handleLogin}
                  className="text-teal-500 px-4 py-2 hover:bg-teal-50 rounded-lg"
                >
                  Log in
                </button>
              </div>
            )}
          </>

          <button className="bg-teal-600 hover:bg-teal-600 text-white py-2 px-4 rounded-lg" onClick={handleSellNow}>
            Sell now
          </button>

          <div className="relative inline-block text-left" ref={dropdownRef}>
            <button
              className="text-gray-400 hover:bg-gray-100 py-2 px-4 rounded-lg"
              onClick={() => setIsOpen(!isOpen)}
            >
              {language.toUpperCase()}<span className="text-sm ml-1">▼</span>
            </button>

            {isOpen && (
              <div className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded shadow-md z-50">
                <button
                  onClick={() => handleLanguageChange("en")}
                  className={`block w-full px-4 py-2 text-left rtl:text-right border border-b-gray-200 hover:bg-gray-100 ${language === "en" ? "font-semibold text-black" : "text-gray-500 "
                    }`}
                >
                  English
                </button>
                <button
                  onClick={() => handleLanguageChange("ar")}
                  className={`block w-full px-4 py-2 text-left rtl:text-right hover:bg-gray-100 ${language === "ar" ? "font-semibold text-black" : "text-gray-500"
                    }`}
                >
                  العربية
                </button>
              </div>
            )}
          </div>

        </div>

        {/* Mobile Menu Toggle - visible on mobile and tablet */}
        <button
          className="lg:hidden flex items-center"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Navigation Categories and Mega Menu - desktop only */}
      <div className="border-t border-gray-200" />
      <div
        className="relative w-full"
        onMouseEnter={() => setIsMegaMenuOpen(true)}
        onMouseLeave={() => {
          setIsMegaMenuOpen(false);
          setActiveCategory(null);
        }}
      >
        <div className="container mx-auto hidden lg:flex items-center space-x-4 py-2 px-4 w-full overflow-x-auto">
          <HeaderCategories />
        </div>

        {isMegaMenuOpen && (
          <div className="absolute top-full left-0 w-full bg-white shadow-lg z-50">
            <MegaMenu />
          </div>
        )}
      </div>
      <div className="border-t border-gray-200" />

      {/* Mobile Menu */}
      {isMobileMenuOpen && <MobileMenu />}
    </header>
  );
};

export default Header;
