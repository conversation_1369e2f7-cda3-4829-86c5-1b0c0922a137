import React, { useState, useEffect, useRef } from 'react';
import { adminApi } from '../../services/adminApi';

const Menus = () => {
    const [menus, setMenus] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [editingMenu, setEditingMenu] = useState(null);
    const [formData, setFormData] = useState({
        name: '',
        categories: [],
        subcategories: []
    });
    const [searchTerm, setSearchTerm] = useState({ categories: '', subcategories: '' });
    const [isDropdownOpen, setIsDropdownOpen] = useState({ categories: false, subcategories: false });
    const dropdownRef = useRef(null);

    // State for fetched categories data with subcategories
    const [categoryDataWithSubcategories, setCategoryDataWithSubcategories] = useState([]);
    // State for categories options for dropdown { value: '...', label: '...' }
    const [categoryOptions, setCategoryOptions] = useState([]);
    // State for all unique subcategories options for display { value: '...', label: '...' }
    const [allSubcategoryOptions, setAllSubcategoryOptions] = useState([]);

    // State for subcategories options filtered by selected categories { value: '...', label: '...' }
    const [filteredSubcategoryOptions, setFilteredSubcategoryOptions] = useState([]);

    useEffect(() => {
        fetchMenus();
        fetchCategoriesAndSubcategories();
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                // Only close if clicking outside both dropdowns
                const isClickingDropdownContent = event.target.closest('.dropdown-content');
                if (!isClickingDropdownContent) {
                    setIsDropdownOpen({ categories: false, subcategories: false });
                }
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Effect to update filtered subcategories when selected categories or available data changes
    useEffect(() => {
        updateFilteredSubcategories(formData.categories);
    }, [formData.categories, categoryDataWithSubcategories]);

    const fetchMenus = async () => {
        try {
            const res = await adminApi.getMenus();
            setMenus(res.data.menus || []);
        } catch (err) {
            console.error('Error fetching menus:', err);
            alert('Failed to fetch menus');
        }
    };

    // Test function to debug API response (you can call this from browser console)
    window.testCategoriesAPI = async () => {
        try {
            const res = await adminApi.getCategories();
            console.log('🔍 Raw API Response:', res);
            console.log('🔍 Response type:', typeof res);
            console.log('🔍 Response keys:', Object.keys(res || {}));
            if (res?.categories) {
                console.log('🔍 Categories array:', res.categories);
                console.log('🔍 First category:', res.categories[0]);
                if (res.categories[0]) {
                    console.log('🔍 First category keys:', Object.keys(res.categories[0]));
                }
            }
        } catch (error) {
            console.error('❌ API Test Error:', error);
        }
    };

    const fetchCategoriesAndSubcategories = async () => {
        try {
            const res = await adminApi.getCategories();
            console.log('🔍 Full API Response:', res);

            // Based on backend code: response structure is { message: 'Categories fetched', categories: [...] }
            const data = res?.categories || [];

            if (!Array.isArray(data)) {
                console.error('❌ Categories is not an array:', data);
                alert('Invalid data format received from API');
                return;
            }

            console.log('📊 Extracted categories data:', data);
            console.log('📊 Categories count:', data.length);

            if (!Array.isArray(data)) {
                console.error('❌ Data is not an array:', data);
                alert('Invalid data format received from API');
                return;
            }

            setCategoryDataWithSubcategories(data);

            // Map backend data to frontend format
            // Backend structure: { _id, name, subCategories: [{ name, slug, childCategories: [...] }] }
            const categories = data?.map(item => {
                const categoryName = item.name; // Backend uses 'name' property

                if (!categoryName) {
                    console.warn('⚠️ No category name found for item:', item);
                    return null;
                }

                return { value: categoryName, label: categoryName };
            }).filter(Boolean); // Remove null values

            console.log('✅ Mapped categories:', categories);
            setCategoryOptions(categories);

            // Collect all unique subcategories from all categories for display purposes in the table
            // Backend structure: subCategories: [{ name, slug, childCategories: [...] }]
            const allSubs = data.flatMap(item => {
                const subcategories = item.subCategories || []; // Backend uses 'subCategories'
                if (!Array.isArray(subcategories)) {
                    console.warn('⚠️ Subcategories not an array for item:', item);
                    return [];
                }
                return subcategories.map(sub => ({
                    value: sub.name, // Backend subcategory has 'name' property
                    label: sub.name
                }));
            });

            const uniqueAllSubs = Array.from(new Set(allSubs.map(sub => sub.value)))
                                            .map(value => allSubs.find(sub => sub.value === value));

            console.log('✅ All subcategories:', uniqueAllSubs);
            setAllSubcategoryOptions(uniqueAllSubs);

        } catch (err) {
            console.error('❌ Error fetching categories and subcategories:', err);
            alert('Failed to fetch categories and subcategories: ' + err.message);
        }
    };

    const updateFilteredSubcategories = (selectedCategories) => {
        console.log('🔄 Updating filtered subcategories for categories:', selectedCategories);
        console.log('📊 Available category data:', categoryDataWithSubcategories);

        if (selectedCategories.length === 0) {
            setFilteredSubcategoryOptions([]);
            setFormData(prev => ({ ...prev, subcategories: [] })); // Clear selected subcategories if no categories are selected
            return;
        }

        // Backend data structure: { _id, name, subCategories: [{ name, slug, childCategories: [...] }] }
        const relevantSubcategories = categoryDataWithSubcategories
            .filter(categoryData => {
                const categoryName = categoryData.name; // Backend uses 'name' property
                const isSelected = selectedCategories.includes(categoryName);
                console.log(`🔍 Category "${categoryName}" selected:`, isSelected);
                return isSelected;
            })
            .flatMap(categoryData => {
                const subcategories = categoryData.subCategories || []; // Backend uses 'subCategories'
                console.log(`📋 Subcategories for "${categoryData.name}":`, subcategories);
                return subcategories.map(sub => ({
                    value: sub.name, // Backend subcategory has 'name' property
                    label: sub.name
                }));
            });

        console.log('🎯 Relevant subcategories:', relevantSubcategories);

        // Remove duplicates and update filtered options
        const uniqueRelevantSubcategories = Array.from(new Set(relevantSubcategories.map(sub => sub.value)))
                                          .map(value => relevantSubcategories.find(sub => sub.value === value));

        console.log('✅ Unique filtered subcategories:', uniqueRelevantSubcategories);
        setFilteredSubcategoryOptions(uniqueRelevantSubcategories);

         // Also filter the currently selected subcategories to keep only those available
        setFormData(prev => ({
            ...prev,
            subcategories: prev.subcategories.filter(sub => uniqueRelevantSubcategories.some(filteredSub => filteredSub.value === sub))
        }));
    };

    const handleAddEdit = (menu = null) => {
        setEditingMenu(menu);

        let initialFormData;
        if (menu) {
            // Convert object formats back to strings for editing
            const categories = menu.categories.map(cat =>
                typeof cat === 'string' ? cat : cat.name
            );
            const subcategories = menu.subcategories.map(sub =>
                typeof sub === 'string' ? sub : sub.name
            );

            initialFormData = {
                name: menu.name,
                categories: categories,
                subcategories: subcategories
            };

            console.log('🔄 Editing menu - converted data:', initialFormData);
        } else {
            initialFormData = { name: '', categories: [], subcategories: [] };
        }

        setFormData(initialFormData);
        setIsModalVisible(true);

        // When editing, filter subcategories based on the menu's existing categories
        if (menu) {
            updateFilteredSubcategories(initialFormData.categories);
        } else {
            // When adding, clear filtered subcategories initially
            setFilteredSubcategoryOptions([]);
        }
    };

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this menu?')) {
            try {
                await adminApi.deleteMenu(id);
                fetchMenus();
            } catch (err) {
                console.error('Delete failed', err);
                alert('Failed to delete menu');
            }
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Validate form data
        if (!formData.name || formData.name.trim() === '') {
            alert('Menu name is required');
            return;
        }

        if (!formData.categories || formData.categories.length === 0) {
            alert('At least one category must be selected');
            return;
        }

        console.log('📤 Submitting menu data:', formData);
        console.log('📤 Categories:', formData.categories);
        console.log('📤 Subcategories:', formData.subcategories);

        try {
            if (editingMenu) {
                console.log('📝 Updating menu:', editingMenu._id);
                await adminApi.updateMenu(editingMenu._id, formData);
            } else {
                console.log('➕ Creating new menu');
                await adminApi.createMenu(formData);
            }
            setIsModalVisible(false);
            fetchMenus();
            alert('Menu saved successfully!');
        } catch (err) {
            console.error('❌ Save failed:', err);
            console.error('❌ Error response:', err.response?.data);
            console.error('❌ Error status:', err.response?.status);
            console.error('❌ Error headers:', err.response?.headers);

            const errorMessage = err.response?.data?.message || err.message || 'Unknown error occurred';
            alert('Failed to save menu: ' + errorMessage);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSearchChange = (type, value) => {
        setSearchTerm(prev => ({ ...prev, [type]: value }));
    };

    const toggleDropdown = (type) => {
        setIsDropdownOpen(prev => ({ ...prev, [type]: !prev[type] }));
    };

    const handleCategoryCheckboxChange = (value) => {
        setFormData(prev => {
            const currentCategories = prev.categories;
            const newCategories = currentCategories.includes(value)
                ? currentCategories.filter(v => v !== value)
                : [...currentCategories, value];

            // updateFilteredSubcategories will be called by the useEffect hook

            return { ...prev, categories: newCategories };
        });
    };

    const handleSubcategoryCheckboxChange = (value) => {
        setFormData(prev => {
            const currentSubcategories = prev.subcategories;
            const newSubcategories = currentSubcategories.includes(value)
                ? currentSubcategories.filter(v => v !== value)
                : [...currentSubcategories, value];
            return { ...prev, subcategories: newSubcategories };
        });
    };

    const filteredOptions = (type) => {
        if (type === 'categories') {
            const filtered = categoryOptions.filter(option =>
                option.label.toLowerCase().includes(searchTerm.categories.toLowerCase())
            );
            console.log('🔍 Filtered categories:', filtered);
            return filtered;
        } else { // type === 'subcategories'
            // Use the filtered list of subcategories
            const filtered = filteredSubcategoryOptions.filter(option =>
                option.label.toLowerCase().includes(searchTerm.subcategories.toLowerCase())
            );
            console.log('🔍 Filtered subcategories:', filtered);
            console.log('🔍 Available filteredSubcategoryOptions:', filteredSubcategoryOptions);
            console.log('🔍 Search term:', searchTerm.subcategories);
            return filtered;
        }
    };

    const getSelectedLabels = (type) => {
        const options = type === 'categories' ? categoryOptions : allSubcategoryOptions; // Use appropriate options list for displaying labels
        return formData[type]
            .map(value => options.find(opt => opt.value === value)?.label)
            .filter(Boolean)
            .join(', ');
    };

    return (
        <div className="p-6">
            <div className="mb-6">
                <button
                    onClick={() => handleAddEdit()}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
                >
                    <span className="mr-2">+</span> Add Menu
                </button>
            </div>

            <div className="overflow-x-auto">
                <table className="min-w-full bg-white border border-gray-200">
                    <thead>
                        <tr className="bg-gray-100">
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Categories</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subcategories</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                        {menus.map(menu => (
                            <tr key={menu._id}>
                                <td className="px-6 py-4 whitespace-nowrap">{menu.name}</td>
                                <td className="px-6 py-4">
                                    <div className="flex flex-wrap gap-1">
                                        {menu.categories.map((category, index) => {
                                            // Handle both string and object formats
                                            const categoryName = typeof category === 'string' ? category : category.name;
                                            const categoryKey = typeof category === 'string' ? category : category._id || index;

                                            return (
                                                <span key={categoryKey} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                                                    {categoryName}
                                                </span>
                                            );
                                        })}
                                    </div>
                                </td>
                                <td className="px-6 py-4">
                                    <div className="flex flex-wrap gap-1">
                                        {menu.subcategories.map((subcategory, index) => {
                                            // Handle both string and object formats
                                            const subcategoryName = typeof subcategory === 'string' ? subcategory : subcategory.name;
                                            const subcategoryKey = typeof subcategory === 'string' ? subcategory : subcategory._id || index;

                                            return (
                                                <span key={subcategoryKey} className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                                                    {subcategoryName}
                                                </span>
                                            );
                                        })}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <button
                                        onClick={() => handleAddEdit(menu)}
                                        className="text-blue-500 hover:text-blue-600 mr-4"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        onClick={() => handleDelete(menu._id)}
                                        className="text-red-500 hover:text-red-600"
                                    >
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {isModalVisible && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="bg-white p-6 rounded-lg w-[500px]">
                        <h2 className="text-xl font-bold mb-4">
                            {editingMenu ? 'Edit Menu' : 'Add Menu'}
                        </h2>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Menu Name
                                </label>
                                <input
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>

                            <div className="mb-4" ref={dropdownRef}>
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Categories
                                </label>
                                <div className="relative">
                                    <div
                                        onClick={() => toggleDropdown('categories')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer flex items-center justify-between"
                                    >
                                        <span className="text-gray-600">
                                            {getSelectedLabels('categories') || 'Select categories...'}
                                        </span>
                                        <svg
                                            className={`w-4 h-4 transition-transform ${
                                                isDropdownOpen.categories ? 'rotate-180' : ''
                                            }`}
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </div>
                                    {isDropdownOpen.categories && (
                                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto dropdown-content">
                                            <div className="p-2">
                                                <input
                                                    type="text"
                                                    value={searchTerm.categories}
                                                    onChange={(e) => handleSearchChange('categories', e.target.value)}
                                                    placeholder="Search categories..."
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md mb-2"
                                                />
                                                {filteredOptions('categories').map(option => (
                                                    <div key={option.value} className="flex items-center p-2 hover:bg-gray-100">
                                                        <input
                                                            type="checkbox"
                                                            id={`category-${option.value}`}
                                                            checked={formData.categories.includes(option.value)}
                                                            onChange={() => handleCategoryCheckboxChange(option.value)}
                                                            className="mr-2"
                                                        />
                                                        <label htmlFor={`category-${option.value}`} className="cursor-pointer">
                                                            {option.label}
                                                        </label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="mb-4" ref={dropdownRef}>
                                <label className="block text-gray-700 text-sm font-bold mb-2">
                                    Subcategories
                                </label>
                                <div className="relative">
                                    <div
                                        onClick={() => toggleDropdown('subcategories')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer flex items-center justify-between"
                                    >
                                        <span className="text-gray-600">
                                            {getSelectedLabels('subcategories') || 'Select subcategories...'}
                                        </span>
                                        <svg
                                            className={`w-4 h-4 transition-transform ${
                                                isDropdownOpen.subcategories ? 'rotate-180' : ''
                                            }`}
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </div>
                                    {isDropdownOpen.subcategories && (
                                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto dropdown-content">
                                            <div className="p-2">
                                                <input
                                                    type="text"
                                                    value={searchTerm.subcategories}
                                                    onChange={(e) => handleSearchChange('subcategories', e.target.value)}
                                                    placeholder="Search subcategories..."
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md mb-2"
                                                />
                                                {filteredOptions('subcategories').length > 0 ? (
                                                    filteredOptions('subcategories').map(option => (
                                                        <div key={option.value} className="flex items-center p-2 hover:bg-gray-100">
                                                            <input
                                                                type="checkbox"
                                                                id={`subcategory-${option.value}`}
                                                                checked={formData.subcategories.includes(option.value)}
                                                                onChange={() => handleSubcategoryCheckboxChange(option.value)}
                                                                className="mr-2"
                                                            />
                                                            <label htmlFor={`subcategory-${option.value}`} className="cursor-pointer">
                                                                {option.label}
                                                            </label>
                                                        </div>
                                                    ))
                                                ) : (
                                                    <div className="p-2 text-gray-500 text-center">
                                                        {formData.categories.length === 0
                                                            ? 'Please select categories first'
                                                            : 'No subcategories available for selected categories'
                                                        }
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="flex justify-end space-x-2">
                                <button
                                    type="button"
                                    onClick={() => setIsModalVisible(false)}
                                    className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                                >
                                    {editingMenu ? 'Update' : 'Add'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Menus;
