import React from 'react';
import { <PERSON><PERSON><PERSON>riangle, Wifi, WifiOff, RefreshCw, X } from 'lucide-react';

const NetworkErrorModal = ({ 
  isOpen, 
  onClose, 
  onRetry, 
  title = "Network Connection Error",
  message = "Network is not connected. Please check your internet connection and try again later.",
  showRetryButton = true,
  autoRetry = false,
  retryCount = 0,
  maxRetries = 3
}) => {
  if (!isOpen) return null;

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const getErrorIcon = () => {
    if (navigator.onLine === false) {
      return <WifiOff className="w-16 h-16 text-red-500 mx-auto mb-4" />;
    }
    return <AlertTriangle className="w-16 h-16 text-orange-500 mx-auto mb-4" />;
  };

  const getErrorTitle = () => {
    if (navigator.onLine === false) {
      return "No Internet Connection";
    }
    return title;
  };

  const getErrorMessage = () => {
    if (navigator.onLine === false) {
      return "You are currently offline. Please check your internet connection and try again.";
    }
    return message;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[9999]">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all">
        {/* Close Button */}
        <div className="flex justify-end p-4 pb-0">
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-8 pt-4 text-center">
          {/* Error Icon */}
          {getErrorIcon()}

          {/* Title */}
          <h2 className="text-2xl font-semibold text-gray-900 mb-3">
            {getErrorTitle()}
          </h2>

          {/* Message */}
          <p className="text-gray-600 mb-6 leading-relaxed">
            {getErrorMessage()}
          </p>

          {/* Connection Status */}
          <div className="flex items-center justify-center mb-6 p-3 bg-gray-50 rounded-lg">
            {navigator.onLine ? (
              <>
                <Wifi className="w-5 h-5 text-green-500 mr-2" />
                <span className="text-sm text-green-600 font-medium">Internet Connected</span>
              </>
            ) : (
              <>
                <WifiOff className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-sm text-red-600 font-medium">No Internet Connection</span>
              </>
            )}
          </div>

          {/* Retry Information */}
          {retryCount > 0 && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">
                Retry attempt {retryCount} of {maxRetries}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 mb-6">
            {showRetryButton && (
              <button
                onClick={handleRetry}
                disabled={retryCount >= maxRetries}
                className="flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                {retryCount >= maxRetries ? 'Max Retries Reached' : 'Try Again'}
              </button>
            )}
            
            <button
              onClick={handleClose}
              className="px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-lg font-medium transition-colors"
            >
              Close
            </button>
          </div>

          {/* Tips */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg text-left">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Troubleshooting Tips:</h4>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Check your WiFi or mobile data connection</li>
              <li>• Try refreshing the page</li>
              <li>• Check if the admin server is running</li>
              <li>• Verify the backend server is accessible</li>
              <li>• Contact your system administrator if the issue persists</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkErrorModal;
