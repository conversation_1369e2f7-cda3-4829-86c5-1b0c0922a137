import apiService from "./ApiService";

export const addProduct = (payload) =>
    apiService({
        url: '/product/sell-product',
        method: 'POST',
        data: payload,
        withAuth: true,
    });

export const updateProduct = (id, payload) =>
    apiService({
        url: `/product/${id}`,
        method: 'PUT',
        data: payload,
        withAuth: true,
    });

export const deleteProduct = (id) =>
    apiService({
        url: `/product/${id}`,
        method: 'DELETE',
        // data: payload,
        withAuth: true,
    });

export const getProduct = () =>
    apiService({
        url: '/product/my-products',
        method: 'GET',
        withAuth: true,
        credentials: 'include',
    });

export const getProductDetails = (id) =>
    apiService({
        url: `/product/${id}`,
        method: 'GET',
        // withAuth: true,
        credentials: 'include',
    });

export const getAllProduct = () =>
    apiService({
        url: '/product/all',
        method: 'GET',
        // withAuth: true,
        credentials: 'include',
    });

export const getUserProduct = (id) =>
    apiService({
        url: `/product/${id}/items`,
        method: 'GET',
        // withAuth: true,
        credentials: 'include',
    });

export const getFevItems = () =>
    apiService({
        url: "/product/favorites",
        method: 'GET',
        withAuth: true,
        credentials: 'include',
    });

export const addFevProduct = (id) =>
    apiService({
        url: `/product/${id}/favorite`,
        method: 'POST',
        // data: payload,
        withAuth: true,
    });

export const getAllCategory = () =>
    apiService({
        url: '/general/category',
        method: 'GET',
        // withAuth: true,
        credentials: 'include',
    });

export const getSize = (id) =>
    apiService({
        url: `/general/category/size/${id}`,
        method: 'GET',
        // withAuth: true,
        credentials: 'include',
    });

export const getFollowers = (id) =>
    apiService({
        url: `/profile/users/${id}/followers`,
        method: 'GET',
        // withAuth: true,
        credentials: 'include',
    });

export const getFollowing = (id) =>
    apiService({
        url: `/profile/users/${id}/following`,
        method: 'GET',
        // withAuth: true,
        credentials: 'include',
    });

export const follow = (id) =>
    apiService({
        url: `/profile/users/${id}/follow`,
        method: 'POST',
        withAuth: true,
        credentials: 'include',
    });

export const unFollow = (id) =>
    apiService({
        url: `/profile/users/${id}/unfollow`,
        method: 'POST',
        withAuth: true,
        credentials: 'include',
    });

export const hideProduct = (id, payload) =>
    apiService({
        url: `/product/${id}/hide-toggle`,
        method: 'POST',
        withAuth: true,
        data: payload,
        credentials: 'include',
    });

// Product management actions
export const bumpProduct = (id) =>
    apiService({
        url: `/product/${id}/bump`,
        method: 'POST',
        withAuth: true,
    });

export const markProductAsSold = (id) =>
    apiService({
        url: `/product/${id}/mark-sold`,
        method: 'POST',
        withAuth: true,
    });

export const markProductAsReserved = (id) =>
    apiService({
        url: `/product/${id}/mark-reserved`,
        method: 'POST',
        withAuth: true,
    });

export const reactivateProduct = (id) =>
    apiService({
        url: `/product/${id}/reactivate`,
        method: 'POST',
        withAuth: true,
    });


