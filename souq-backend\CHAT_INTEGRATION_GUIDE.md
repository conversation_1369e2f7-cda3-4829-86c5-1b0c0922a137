# SOUQ Chat System Integration Guide

This guide explains how to integrate the real-time chat functionality into your SOUQ marketplace application.

## 🚀 Features

- ✅ Real-time messaging with WebSocket
- ✅ Product-based chat rooms
- ✅ Message history and pagination
- ✅ Typing indicators
- ✅ Message seen/delivered status
- ✅ Online/offline status
- ✅ Unread message counts
- ✅ User authentication
- ✅ Responsive design
- ✅ Chat list with last message preview

## 📁 Backend Structure

```
app/user/chat/
├── controllers/
│   └── chatController.js     # Chat API endpoints
└── routes/
    └── chatRoutes.js         # Chat routes

db/models/
├── chatModel.js              # Enhanced chat model
└── messageModel.js           # Enhanced message model

utils/
└── socket.js                 # WebSocket implementation
```

## 🔧 Backend Setup

### 1. Database Models

The chat system uses two main models:

**Chat Model** (`db/models/chatModel.js`):
- Links buyer, seller, and product
- Manages room IDs for WebSocket connections
- Tracks last message and activity

**Message Model** (`db/models/messageModel.js`):
- Stores individual messages
- Handles message status (sent, delivered, seen)
- Supports different message types

### 2. API Endpoints

All chat endpoints are under `/api/user/chat/`:

```javascript
POST   /api/user/chat/product/:productId    # Create/get chat for product
GET    /api/user/chat/                      # Get user's chats
GET    /api/user/chat/:chatId/messages      # Get chat messages
POST   /api/user/chat/:chatId/messages      # Send message (HTTP fallback)
PATCH  /api/user/chat/:chatId/seen          # Mark messages as seen
```

### 3. WebSocket Events

**Client to Server:**
- `join_chat` - Join a chat room
- `send_message` - Send a message
- `typing_start` - Start typing indicator
- `typing_stop` - Stop typing indicator
- `mark_seen` - Mark messages as seen
- `leave_chat` - Leave chat room

**Server to Client:**
- `new_message` - Receive new message
- `user_typing` - Typing indicator
- `messages_seen` - Messages marked as seen
- `user_joined` - User joined chat
- `user_left` - User left chat
- `user_online` - User came online
- `user_offline` - User went offline

## 🎨 Frontend Components

### 1. ChatRoom Component

Main chat interface for real-time messaging:

```jsx
import ChatRoom from './components/ChatRoom';

<ChatRoom
  productId="product_id_here"
  currentUserId="current_user_id"
  onClose={() => setShowChat(false)}
/>
```

### 2. ChatList Component

Shows all user conversations:

```jsx
import ChatList from './components/ChatList';

<ChatList
  onChatSelect={(chat) => handleChatSelect(chat)}
  currentUserId="current_user_id"
/>
```

### 3. ProductChatButton Component

Button for product pages to start chat with seller:

```jsx
import ProductChatButton from './components/ProductChatButton';

<ProductChatButton
  productId="product_id_here"
  currentUserId="current_user_id"
  buttonText="Message seller"
  buttonStyle={{ backgroundColor: '#28a745' }}
/>
```

## 🔗 Integration Steps

### Step 1: Backend Integration

1. **Install Dependencies** (if not already installed):
```bash
npm install socket.io jsonwebtoken
```

2. **Update User Routes** (`app/user/index.js`):
```javascript
const chatRoutes = require('./chat/routes/chatRoutes');
router.use('/chat', chatRoutes);
```

3. **Ensure WebSocket is initialized** in your main server file:
```javascript
const initSocket = require('./utils/socket');
const server = http.createServer(app);
initSocket(server);
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

### Step 2: Frontend Integration

1. **Install Dependencies**:
```bash
npm install socket.io-client axios
```

2. **Add Chat Components** to your React app:
   - Copy the components from `client/chat-app/src/components/`
   - Import and use them in your product pages and chat sections

3. **Product Detail Page Integration**:
```jsx
import ProductChatButton from './components/ProductChatButton';

function ProductDetailPage({ product, currentUser }) {
  return (
    <div>
      {/* Product details */}
      <div className="product-actions">
        <button>Buy now</button>
        <button>Make an offer</button>
        <ProductChatButton
          productId={product.id}
          currentUserId={currentUser.id}
          buttonText="Message seller"
        />
      </div>
    </div>
  );
}
```

4. **Chat Page Integration**:
```jsx
import ChatList from './components/ChatList';
import ChatRoom from './components/ChatRoom';

function ChatPage({ currentUser }) {
  const [selectedChat, setSelectedChat] = useState(null);

  return (
    <div className="chat-page">
      <div className="chat-sidebar">
        <ChatList
          onChatSelect={setSelectedChat}
          currentUserId={currentUser.id}
        />
      </div>
      <div className="chat-main">
        {selectedChat ? (
          <ChatRoom
            productId={selectedChat.product.id}
            currentUserId={currentUser.id}
          />
        ) : (
          <div>Select a chat to start messaging</div>
        )}
      </div>
    </div>
  );
}
```

### Step 3: Authentication Setup

Ensure your authentication token is available to the chat components:

```javascript
// Store token when user logs in
localStorage.setItem('authToken', userToken);

// Update the getToken function in chat components
const getToken = () => {
  return localStorage.getItem('authToken');
};
```

### Step 4: Styling

Add the provided CSS styles to your application or customize them to match your design system.

## 🔒 Security Considerations

1. **Authentication**: All chat endpoints require valid JWT tokens
2. **Authorization**: Users can only access chats they're participants in
3. **Input Validation**: All message content is validated and sanitized
4. **Rate Limiting**: Consider adding rate limiting for message sending

## 📱 Mobile Responsiveness

The chat components are designed to be mobile-friendly:
- Responsive layouts
- Touch-friendly interface
- Optimized for small screens

## 🚀 Testing

1. **Start the backend server**:
```bash
npm run start:user
```

2. **Start the frontend demo**:
```bash
cd client/chat-app
npm start
```

3. **Test the functionality**:
   - Open multiple browser tabs with different users
   - Test real-time messaging
   - Verify typing indicators
   - Check message status updates

## 🔧 Customization

### Styling
Modify the inline styles in components or extract them to CSS classes.

### Message Types
Extend the message model to support images, files, or other content types.

### Notifications
Add push notifications for new messages when users are offline.

### Moderation
Implement message moderation and reporting features.

## 📞 Support

For questions or issues with the chat integration, refer to the code comments or create an issue in the project repository.

## 🎯 Next Steps

1. Integrate with your existing user authentication system
2. Customize the UI to match your brand
3. Add push notifications
4. Implement message search functionality
5. Add file/image sharing capabilities
