import React, { useEffect, useState } from 'react';
import AuthModal from '../components/Auth/AuthModal';
import ProductGrid from '../components/Products/ProductGrid';
import { products } from '../data/products';
import { useAppContext } from '../context/AppContext';
import LoginModal from '../components/Auth/LoginModal';
import ForgotPasswordModal from '../components/Auth/ForgotPasswordModal';
import SignUpModal from '../components/Auth/SignUpModal';
import Filter from '../components/Navigation/Filter';
import { useNavigate } from 'react-router-dom';
import { getAllProduct } from '../api/ProductService';
import LoadingSpinner from '../components/common/LoadingSpinner';


const HomePage = () => {
  const {
    setIsAuthModalOpen,
    setAuthMode,
  } = useAppContext();

  const isAuthenticated = localStorage.getItem("user");
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState(false);
  const [product, setProduct] = useState([])
  const [apiRefresh, setApiRefresh] = useState("")

  useEffect(() => {
    setIsLoading(true); // Start loading
    getAllProduct()
      .then((res) => {
        const items = res?.data?.data?.items || [];
        setProduct(items);
      })
      .catch((err) => {
        console.log(err, "err");
      })
      .finally(() => {
        setIsLoading(false); // Stop loading
      });
  }, [apiRefresh]);

  const handleLogin = () => {
    if (isAuthenticated) {
      navigate("/sell-now")
    } else {
      setAuthMode('login');
      setIsAuthModalOpen(true);
    }
  };


  return (
    <>
      {/* Banner section OUTSIDE of the container */}
      <div className="relative w-full h-[500px] mb-12">
        {/* Background image */}
        <img
          src="https://images.pexels.com/photos/5698851/pexels-photo-5698851.jpeg"
          alt="People with clothes"
          className="absolute inset-0 w-full h-full object-cover"
        />

        {/* Overlay */}
        <div className="absolute inset-0 bg-black/20" />

        {/* Responsive and aligned card */}
        <div className="relative z-10 flex items-center h-full container mx-auto px-4">
          <div className="bg-white rounded-lg shadow-lg p-6 sm:p-8 w-full max-w-sm">
            <h2 className="text-2xl sm:text-3xl font-semibold text-gray-800 mb-4 text-center sm:text-left">
              Ready to declutter your closet?
            </h2>
            <button className="w-full bg-teal-600 text-white py-2 rounded-md hover:bg-teal-700 transition-colors mb-2 mt-2" onClick={handleLogin}>
              Sell now
            </button>
            <a href="#" className="block text-center text-sm text-teal-600 hover:underline mt-2">
              Learn how it works
            </a>
          </div>
        </div>
      </div>

      <Filter />

      {/* Main container for rest of the content */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col gap-6">
          <div className="flex-grow">
            {isLoading ? (
              <div className="flex justify-center py-10">
                <LoadingSpinner />
              </div>
            ) : Array.isArray(product) && product.length === 0 ? (
              <div className="text-center text-gray-500 py-10">No products found.</div>
            ) : (
              <ProductGrid products={product} apiRefresh={apiRefresh} setApiRefresh={setApiRefresh} />
            )}

          </div>

        </div>
        <AuthModal />
        <LoginModal />
        <ForgotPasswordModal />
        <SignUpModal />
      </div>
    </>

  );
};

export default HomePage;
