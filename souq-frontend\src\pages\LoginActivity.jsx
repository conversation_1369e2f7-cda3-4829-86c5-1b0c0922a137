import React from "react";

const sessions = [
    {
        location: "Miami, United States",
        time: "Now",
        current: true,
        browser: "Chrome, Windows",
    },
    {
        location: "Fatehābād, India",
        time: "6 hours ago",
        current: false,
        browser: "Chrome, Windows",
    },
    {
        location: "Fatehābād, India",
        time: "7 hours ago",
        current: false,
        browser: "Chrome, Windows",
    },
    {
        location: "Ahmedabad, India",
        time: "1 week ago",
        current: false,
        browser: "Chrome, Windows",
    },
    {
        location: "Ahmedabad, India",
        time: "1 week ago",
        current: false,
        browser: "Chrome, Windows",
    },
    {
        location: "Ahmedabad, India",
        time: "1 week ago",
        current: false,
        browser: "Chrome, Windows",
    },
];

const LoginActivity = () => {
    return (
        <div className="max-w-7xl mx-auto p-6 bg-white rounded-lg shadow-md mt-5 mb-5">
            <h2 className="text-lg font-semibold">Review login activity</h2>
            <p className="text-sm text-gray-600 mb-6">
                Each session shows a device logged into your account. If you notice any unusual activity, log out of that session.
            </p>

            {sessions.map((session, index) => (
                <div
                    key={index}
                    className={`flex items-center justify-between py-4 ${index !== sessions.length - 1 ? "border-b" : ""
                        }`}
                >
                    <div>
                        <p className="font-medium text-gray-800">
                            {session.location}
                            {session.current && (
                                <span className="text-teal-600 ml-2">- Current Device</span>
                            )}
                        </p>
                        <p className="text-sm text-gray-500">
                            {session.time} · {session.browser}
                        </p>
                    </div>
                    {!session.current && (
                        <button className="text-teal-600 text-sm hover:underline">
                            Log out
                        </button>
                    )}
                </div>
            ))}
        </div>
    );
};

export default LoginActivity;
