import React, { useState } from "react";
import { FaStar } from "react-icons/fa";

// Sample reviews (make sure each has a "type": "member" or "auto")
const reviews = [
  {
    id: 1,
    user: "Vinted",
    avatar: "https://ui-avatars.com/api/?name=V",
    rating: 5,
    message: "Auto-feedback: Sale completed successfully",
    timeAgo: "2 hours ago",
    type: "auto",
  },
  {
    id: 2,
    user: "aurelia131013",
    avatar: "https://ui-avatars.com/api/?name=A",
    rating: 5,
    message: "Thank you!",
    timeAgo: "2 weeks ago",
    type: "member",
  },
  {
    id: 3,
    user: "shenanigans1972",
    avatar: "https://ui-avatars.com/api/?name=S",
    rating: 5,
    message: "thanks",
    timeAgo: "2 weeks ago",
    type: "member",
  },
  {
    id: 4,
    user: "Vinted",
    avatar: "https://ui-avatars.com/api/?name=V",
    rating: 5,
    message: "Auto-feedback: Sale completed successfully",
    timeAgo: "1 month ago",
    type: "auto",
  },
];

const ReviewSection = () => {
  const [filter, setFilter] = useState("all");

  const filteredReviews = reviews.filter((r) => {
    if (filter === "member") return r.type === "member";
    if (filter === "auto") return r.type === "auto";
    return true;
  });

  return (
    <div className="">
      <div className="flex flex-col items-start">
        {/* Rating and stars in same row */}
        <div className="flex items-center space-x-2">
          <h2 className="text-6xl text-gray-800">5.0</h2>
          <div className="flex text-yellow-400">
            {[...Array(5)].map((_, index) => (
              <FaStar key={index} className="text-yellow-500 text-md" />
            ))}
          </div>
          <p className="text-md text-gray">(4)</p>
        </div>
        {/* Review count */}

      </div>

      {/* Filter and Summary Section */}
      <div className="flex flex-wrap gap-4 items-center mb-6 justify-between mt-4">
        <div className="flex gap-6 text-md text-gray-600">
          <div className="flex items-center gap-2">
            <FaStar className="text-yellow-500 text-md" />
            <span>Member reviews ({reviews.filter(r => r.type === "member").length})</span>
          </div>
          <div className="flex items-center gap-2">
            <FaStar className="text-yellow-500 text-md" />
            <span>Automatic reviews ({reviews.filter(r => r.type === "auto").length})</span>
          </div>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 text-md font-medium">
          <button
            className={`px-4 py-1 rounded-full border ${filter === "all" ? "bg-teal-600 text-white" : "bg-white"}`}
            onClick={() => setFilter("all")}
          >
            All
          </button>
          <button
            className={`px-4 py-1 rounded-full border ${filter === "member" ? "bg-teal-600 text-white" : "bg-white"}`}
            onClick={() => setFilter("member")}
          >
            From members
          </button>
          <button
            className={`px-4 py-1 rounded-full border ${filter === "auto" ? "bg-teal-600 text-white" : "bg-white"}`}
            onClick={() => setFilter("auto")}
          >
            Automatic
          </button>
        </div>
      </div>

      {/* Review List */}
      <div className="space-y-6">
        {filteredReviews.map((review, index) => (
          <div key={review.id} className={`flex gap-4 items-start pb-4 ${index !== filteredReviews.length - 1 ? "border-b" : ""
            }`}>
            <img
              src={review.avatar}
              alt={review.user}
              className="w-14 h-14 rounded-full"
            />
            <div className="flex-grow">
              <div className="flex items-center justify-between">
                <span className="font-semibold text-md text-gray-800">
                  {review.user}
                </span>
                <span className="text-sm text-gray-500">{review.timeAgo}</span>
              </div>
              <div className="flex items-center mb-1">
                {[...Array(review.rating)].map((_, i) => (
                  <FaStar key={i} className="text-yellow-500 text-lg" />
                ))}
              </div>
              <p className="text-md text-gray-700">{review.message}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReviewSection;
