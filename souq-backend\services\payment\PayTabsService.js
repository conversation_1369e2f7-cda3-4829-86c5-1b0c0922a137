const BasePaymentService = require('./BasePaymentService');

class PayTabsService extends BasePaymentService {
  constructor(config) {
    super(config);
    this.baseUrl = this.isTestMode 
      ? 'https://secure.paytabs.com' 
      : 'https://secure.paytabs.com';
    this.profileId = config.configuration?.paytabs?.profileId;
    this.serverKey = config.configuration?.paytabs?.serverKey;
    this.region = config.configuration?.paytabs?.region || 'ARE';
  }

  /**
   * Initialize payment with PayTabs
   * @param {Object} paymentData - Payment details
   * @returns {Promise<Object>} Payment initialization response
   */
  async initializePayment(paymentData) {
    try {
      const validation = this.validatePaymentData(paymentData);
      if (!validation.isValid) {
        return {
          success: false,
          error: 'Validation failed',
          details: validation.errors
        };
      }

      const requestData = {
        profile_id: this.profileId,
        tran_type: 'sale',
        tran_class: 'ecom',
        cart_id: paymentData.orderId,
        cart_description: paymentData.description || 'SOUQ Marketplace Purchase',
        cart_currency: paymentData.currency,
        cart_amount: this.formatAmount(paymentData.amount),
        
        // Customer information
        customer_details: {
          name: paymentData.customerName,
          email: paymentData.customerEmail,
          phone: paymentData.customerPhone,
          street1: paymentData.billingAddress?.street1 || '',
          city: paymentData.billingAddress?.city || '',
          state: paymentData.billingAddress?.state || '',
          country: paymentData.billingAddress?.country || this.region,
          zip: paymentData.billingAddress?.zip || ''
        },
        
        // Shipping information (if different from billing)
        shipping_details: paymentData.shippingAddress ? {
          name: paymentData.shippingAddress.name || paymentData.customerName,
          email: paymentData.customerEmail,
          phone: paymentData.customerPhone,
          street1: paymentData.shippingAddress.street1,
          city: paymentData.shippingAddress.city,
          state: paymentData.shippingAddress.state,
          country: paymentData.shippingAddress.country,
          zip: paymentData.shippingAddress.zip
        } : null,
        
        // URLs
        callback: paymentData.callbackUrl,
        return: paymentData.returnUrl,
        
        // Payment configuration
        payment_methods: ['all'],
        hide_shipping: paymentData.hideShipping || false,
        
        // Additional configuration
        config_id: this.isTestMode ? 1 : 2, // 1 for test, 2 for live
        
        // Metadata
        user_defined: {
          escrow_transaction_id: paymentData.escrowTransactionId,
          buyer_id: paymentData.buyerId,
          seller_id: paymentData.sellerId,
          product_id: paymentData.productId
        }
      };

      const response = await this.makeRequest(
        'POST',
        `${this.baseUrl}/payment/request`,
        requestData,
        {
          'Authorization': this.serverKey,
          'Content-Type': 'application/json'
        }
      );

      this.logTransaction('INITIALIZE_PAYMENT', requestData, response);

      if (response.success && response.data.tran_ref) {
        return {
          success: true,
          transactionId: response.data.tran_ref,
          paymentUrl: response.data.redirect_url,
          gatewayResponse: response.data
        };
      }

      return {
        success: false,
        error: response.data?.message || 'Payment initialization failed',
        gatewayResponse: response.data
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  }

  /**
   * Verify payment status with PayTabs
   * @param {string} transactionId - PayTabs transaction reference
   * @returns {Promise<Object>} Payment status
   */
  async verifyPayment(transactionId) {
    try {
      const requestData = {
        profile_id: this.profileId,
        tran_ref: transactionId
      };

      const response = await this.makeRequest(
        'POST',
        `${this.baseUrl}/payment/query`,
        requestData,
        {
          'Authorization': this.serverKey,
          'Content-Type': 'application/json'
        }
      );

      this.logTransaction('VERIFY_PAYMENT', requestData, response);

      if (response.success && response.data) {
        const paymentStatus = this.mapPaymentStatus(response.data.payment_result?.response_status);
        
        return {
          success: true,
          status: paymentStatus,
          transactionId: response.data.tran_ref,
          amount: response.data.cart_amount,
          currency: response.data.cart_currency,
          gatewayTransactionId: response.data.acquirer_rrn,
          gatewayResponse: response.data,
          paidAt: response.data.payment_result?.response_time ? new Date(response.data.payment_result.response_time) : null
        };
      }

      return {
        success: false,
        error: response.data?.message || 'Payment verification failed',
        gatewayResponse: response.data
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  }

  /**
   * Process refund with PayTabs
   * @param {Object} refundData - Refund details
   * @returns {Promise<Object>} Refund response
   */
  async processRefund(refundData) {
    try {
      const requestData = {
        profile_id: this.profileId,
        tran_type: 'refund',
        tran_class: 'ecom',
        tran_ref: refundData.originalTransactionId,
        cart_id: refundData.orderId,
        cart_description: refundData.reason || 'Refund request',
        cart_currency: refundData.currency,
        cart_amount: this.formatAmount(refundData.amount)
      };

      const response = await this.makeRequest(
        'POST',
        `${this.baseUrl}/payment/request`,
        requestData,
        {
          'Authorization': this.serverKey,
          'Content-Type': 'application/json'
        }
      );

      this.logTransaction('PROCESS_REFUND', requestData, response);

      if (response.success && response.data.tran_ref) {
        return {
          success: true,
          refundId: response.data.tran_ref,
          status: 'processed',
          amount: refundData.amount,
          currency: refundData.currency,
          gatewayResponse: response.data
        };
      }

      return {
        success: false,
        error: response.data?.message || 'Refund processing failed',
        gatewayResponse: response.data
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  }

  /**
   * Handle PayTabs webhook
   * @param {Object} webhookData - Webhook payload
   * @returns {Promise<Object>} Processed webhook data
   */
  async handleWebhook(webhookData) {
    try {
      // PayTabs sends webhook data in the request body
      const {
        tran_ref,
        payment_result,
        cart_id,
        cart_amount,
        cart_currency,
        user_defined
      } = webhookData;

      if (!tran_ref) {
        return {
          success: false,
          error: 'Invalid webhook data: missing transaction reference'
        };
      }

      const paymentStatus = this.mapPaymentStatus(payment_result?.response_status);
      
      return {
        success: true,
        transactionId: tran_ref,
        status: paymentStatus,
        orderId: cart_id,
        amount: parseFloat(cart_amount),
        currency: cart_currency,
        gatewayTransactionId: payment_result?.acquirer_rrn,
        metadata: user_defined,
        rawData: webhookData
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  }

  /**
   * Map PayTabs payment status to standard status
   * @param {string} paytabsStatus - PayTabs status
   * @returns {string} Standardized status
   */
  mapPaymentStatus(paytabsStatus) {
    const statusMap = {
      'A': 'completed',      // Authorized/Approved
      'H': 'completed',      // Hold (successful but on hold)
      'P': 'processing',     // Pending
      'V': 'completed',      // Voided (but payment was successful)
      'E': 'failed',         // Error
      'D': 'failed',         // Declined
      'C': 'cancelled',      // Cancelled
      'F': 'failed',         // Failed
      'N': 'failed'          // Not processed
    };

    return statusMap[paytabsStatus] || 'unknown';
  }

  /**
   * Get PayTabs specific configuration
   * @returns {Object} Configuration details
   */
  getGatewayConfig() {
    return {
      gatewayName: 'paytabs',
      displayName: 'PayTabs',
      supportedCurrencies: ['AED', 'SAR', 'USD', 'EUR'],
      supportedPaymentMethods: ['credit_card', 'debit_card', 'apple_pay'],
      region: this.region,
      isConfigured: !!(this.profileId && this.serverKey)
    };
  }

  /**
   * Test gateway connection
   * @returns {Promise<Object>} Connection test result
   */
  async testConnection() {
    try {
      // Test with a minimal query request
      const response = await this.makeRequest(
        'POST',
        `${this.baseUrl}/payment/query`,
        {
          profile_id: this.profileId,
          tran_ref: 'TEST_CONNECTION'
        },
        {
          'Authorization': this.serverKey,
          'Content-Type': 'application/json'
        }
      );

      return {
        success: true,
        connected: response.success,
        message: response.success ? 'Connection successful' : 'Connection failed',
        details: response.data
      };

    } catch (error) {
      return {
        success: false,
        connected: false,
        message: 'Connection test failed',
        error: error.message
      };
    }
  }
}

module.exports = PayTabsService;
