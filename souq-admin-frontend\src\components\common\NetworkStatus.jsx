import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, AlertTriangle } from 'lucide-react';
import { useNetworkError } from '../../hooks/useNetworkError';

const NetworkStatus = () => {
  const { isOnline } = useNetworkError();
  const [showStatus, setShowStatus] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');

  useEffect(() => {
    if (!isOnline) {
      setStatusMessage('Connection Lost');
      setShowStatus(true);
    } else {
      // Show "Connected" message briefly when connection is restored
      if (showStatus) {
        setStatusMessage('Connected');
        setTimeout(() => {
          setShowStatus(false);
        }, 3000);
      }
    }
  }, [isOnline, showStatus]);

  if (!showStatus) return null;

  return (
    <div className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg transition-all duration-300 ${
      isOnline 
        ? 'bg-green-500 text-white' 
        : 'bg-red-500 text-white'
    }`}>
      <div className="flex items-center gap-2">
        {isOnline ? (
          <Wifi className="w-4 h-4" />
        ) : (
          <WifiOff className="w-4 h-4" />
        )}
        <span className="text-sm font-medium">{statusMessage}</span>
      </div>
    </div>
  );
};

export default NetworkStatus;
