# Order Status Update Fix

## Problem
When clicking the "Mark Shipped" button, the API call to `PUT /api/user/orders/{orderId}/status` was returning:
1. **404 Error**: Order not found
2. **400 Error**: Cannot transition from pending_payment to shipped

## Root Cause Analysis

### 404 Error - Order Not Found
The issue was that the `updateOrderStatus` method in `orderController.js` was only looking for orders in the `Order` collection, but the orders displayed in the frontend were actually coming from the `Transaction` and `StandardPayment` collections.

### 400 Error - Invalid Status Transition
The order status was `pending_payment` but the seller was trying to mark it as `shipped`. The status transition logic required:
- `pending_payment` → `paid` → `processing` → `shipped`

However, for completed payments, sellers should be able to mark orders as shipped directly.

## Solution

### 1. Updated Order Controller (`app/user/shipping/controllers/orderController.js`)

#### A. Enhanced `updateOrderStatus` method:
- **Multi-collection search**: Now searches in Order, Transaction, and StandardPayment collections
- **Improved status mapping**: Maps payment status to order status correctly
- **Flexible transitions**: Allows `paid` → `shipped` directly
- **Auto-transition logic**: If payment is completed, allows `pending_payment` → `shipped`

#### B. Enhanced `confirmDelivery` method:
- Same multi-collection search capability
- Consistent status mapping logic

### 2. Updated Database Models

#### A. Transaction Model (`db/models/transactionModel.js`)
Added new fields for order tracking:
```javascript
// Order status tracking
orderStatus: {
  type: String,
  enum: ['pending_payment', 'paid', 'processing', 'shipped', 'in_transit', 'out_for_delivery', 'delivered', 'cancelled', 'returned', 'refunded'],
  default: 'pending_payment'
},

// Status history for order tracking
statusHistory: [{
  status: String,
  timestamp: { type: Date, default: Date.now },
  description: String,
  updatedBy: { type: String, enum: ['system', 'buyer', 'seller', 'shipping_provider'] }
}],

// Delivery tracking
deliveryConfirmedAt: Date,
deliveryConfirmedBy: { type: String, enum: ['buyer', 'system', 'shipping_provider'] },
deliveryRating: { type: Number, min: 1, max: 5 },
deliveryFeedback: String,
ratedAt: Date
```

#### B. StandardPayment Model (`db/models/standardPaymentModel.js`)
Added the same order tracking fields as Transaction model.

### 3. Status Mapping Logic

The controller now properly maps payment status to order status:
- `completed` or `paid` → `paid`
- `pending` → `pending_payment`
- `processing` → `paid` (payment being processed, order can be processed)
- `failed` or `cancelled` → `cancelled`

### 4. Enhanced Status Transitions

Updated transition rules:
```javascript
const validTransitions = {
  'pending_payment': ['paid', 'cancelled'],
  'paid': ['processing', 'shipped', 'cancelled'], // Added direct paid → shipped
  'processing': ['shipped', 'cancelled'],
  'shipped': ['in_transit', 'delivered'],
  'in_transit': ['out_for_delivery', 'delivered'],
  'out_for_delivery': ['delivered'],
  'delivered': ['returned'],
  'cancelled': [], // Final state
  'returned': [], // Final state
  'refunded': [] // Final state
};
```

### 5. Special Case Handling

Added logic to handle completed payments:
- If payment status is `completed` but order status is `pending_payment`
- And seller is trying to mark as `shipped`
- Auto-transition through `paid` status for validation

## Testing

### Manual Testing Steps:
1. Ensure backend server is running (`npm run dev`)
2. Login as a seller
3. Navigate to Orders page
4. Find an order with `processing` status (or completed payment)
5. Click "Mark Shipped" button
6. Verify the order status updates successfully

### API Testing:
Use the provided test script `test-mark-shipped.js` to test the API directly.

## Files Modified:
1. `souq-backend/app/user/shipping/controllers/orderController.js`
2. `souq-backend/db/models/transactionModel.js`
3. `souq-backend/db/models/standardPaymentModel.js`

## Files Created:
1. `souq-backend/test-order-status-update.js` - Debug script
2. `souq-backend/test-mark-shipped.js` - API test script
3. `souq-backend/ORDER_STATUS_UPDATE_FIX.md` - This documentation

## Next Steps:
1. Test the fix with real orders
2. Monitor for any additional edge cases
3. Consider adding more granular status tracking if needed
4. Update frontend to handle the new status fields if required
