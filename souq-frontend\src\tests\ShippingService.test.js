import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { toast } from 'react-toastify';
import ShippingService from '../api/ShippingService';
import DeliverySettings from '../components/Settings/DeliverySettings';
import Orders from '../pages/Orders';
import OrderDetails from '../pages/OrderDetails';
import TrackingWidget from '../components/Tracking/TrackingWidget';
import DeliveryProgress from '../components/Tracking/DeliveryProgress';

// Mock dependencies
jest.mock('../api/ShippingService');
jest.mock('react-toastify');
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useParams: () => ({ orderId: 'test-order-id' })
}));

describe('Frontend Shipping Components Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    ShippingService.getProviders.mockResolvedValue({
      success: true,
      data: {
        providers: [
          {
            _id: 'provider1',
            name: 'local_pickup',
            displayName: 'Local Pickup',
            supportedServices: [
              {
                serviceCode: 'LOCAL_PICKUP',
                serviceName: 'Local Pickup',
                estimatedDays: { min: 0, max: 1 }
              }
            ],
            pricing: { baseFee: 0, currency: 'AED' }
          },
          {
            _id: 'provider2',
            name: 'aramex',
            displayName: 'Aramex',
            supportedServices: [
              {
                serviceCode: 'PPX',
                serviceName: 'Priority Parcel Express',
                estimatedDays: { min: 1, max: 3 }
              }
            ],
            pricing: { baseFee: 25, currency: 'AED' }
          }
        ]
      }
    });

    ShippingService.getDeliveryOptions.mockResolvedValue({
      success: true,
      data: {
        deliveryOptions: [
          {
            _id: 'option1',
            shippingProvider: {
              _id: 'provider1',
              name: 'local_pickup',
              displayName: 'Local Pickup'
            },
            serviceCode: 'LOCAL_PICKUP',
            serviceName: 'Local Pickup',
            isDefault: true,
            preferences: {
              includeInsurance: false,
              requireSignature: false,
              allowCashOnDelivery: true
            }
          }
        ]
      }
    });
  });

  describe('DeliverySettings Component', () => {
    const renderDeliverySettings = () => {
      return render(
        <BrowserRouter>
          <DeliverySettings />
        </BrowserRouter>
      );
    };

    test('should render delivery settings with providers tab', async () => {
      renderDeliverySettings();

      await waitFor(() => {
        expect(screen.getByText('Delivery Settings')).toBeInTheDocument();
        expect(screen.getByText('Shipping Providers')).toBeInTheDocument();
        expect(screen.getByText('Local Delivery')).toBeInTheDocument();
      });
    });

    test('should load and display shipping providers', async () => {
      renderDeliverySettings();

      await waitFor(() => {
        expect(screen.getByText('Local Pickup')).toBeInTheDocument();
        expect(screen.getByText('Aramex')).toBeInTheDocument();
      });

      expect(ShippingService.getProviders).toHaveBeenCalled();
      expect(ShippingService.getDeliveryOptions).toHaveBeenCalled();
    });

    test('should display existing delivery options', async () => {
      renderDeliverySettings();

      await waitFor(() => {
        expect(screen.getByText('Local Pickup')).toBeInTheDocument();
        expect(screen.getByText('Default')).toBeInTheDocument();
      });
    });

    test('should open add delivery option modal', async () => {
      renderDeliverySettings();

      await waitFor(() => {
        const addButton = screen.getByText('Add Delivery Option');
        fireEvent.click(addButton);
      });

      await waitFor(() => {
        expect(screen.getByText('Add Delivery Option')).toBeInTheDocument();
        expect(screen.getByText('Shipping Provider *')).toBeInTheDocument();
      });
    });

    test('should save new delivery option', async () => {
      ShippingService.saveDeliveryOption.mockResolvedValue({
        success: true,
        data: { deliveryOption: { _id: 'new-option' } }
      });

      renderDeliverySettings();

      // Open modal
      await waitFor(() => {
        const addButton = screen.getByText('Add Delivery Option');
        fireEvent.click(addButton);
      });

      // Fill form
      await waitFor(() => {
        const providerSelect = screen.getByDisplayValue('');
        fireEvent.change(providerSelect, { target: { value: 'provider1' } });
      });

      // Save
      const saveButton = screen.getByText('Save Option');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(ShippingService.saveDeliveryOption).toHaveBeenCalled();
        expect(toast.success).toHaveBeenCalledWith('Delivery option added');
      });
    });

    test('should delete delivery option', async () => {
      ShippingService.deleteDeliveryOption.mockResolvedValue({
        success: true
      });

      // Mock window.confirm
      window.confirm = jest.fn(() => true);

      renderDeliverySettings();

      await waitFor(() => {
        const deleteButton = screen.getByTitle('Delete option');
        fireEvent.click(deleteButton);
      });

      await waitFor(() => {
        expect(ShippingService.deleteDeliveryOption).toHaveBeenCalledWith('option1');
        expect(toast.success).toHaveBeenCalledWith('Delivery option deleted');
      });
    });

    test('should switch to local delivery tab', async () => {
      renderDeliverySettings();

      await waitFor(() => {
        const localTab = screen.getByText('Local Delivery');
        fireEvent.click(localTab);
      });

      // Local delivery content should be rendered
      // This would test the LocalDeliverySettings component
    });
  });

  describe('Orders Component', () => {
    const mockOrders = [
      {
        _id: 'order1',
        orderNumber: 'ORD-**********-000001',
        status: 'shipped',
        createdAt: '2024-01-01T10:00:00Z',
        product: {
          title: 'Test Sneakers',
          price: 150,
          product_photos: ['sneaker1.jpg'],
          brand: 'Nike',
          size: '42'
        },
        seller: {
          username: 'testseller',
          profile_picture: 'seller.jpg'
        },
        shipping: {
          trackingNumber: 'TRACK123',
          estimatedDelivery: '2024-01-05T10:00:00Z'
        },
        timeline: [
          {
            status: 'shipped',
            timestamp: '2024-01-02T10:00:00Z',
            description: 'Package shipped'
          }
        ]
      }
    ];

    beforeEach(() => {
      ShippingService.getOrders.mockResolvedValue({
        success: true,
        data: {
          orders: mockOrders,
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalOrders: 1,
            hasNext: false,
            hasPrev: false
          }
        }
      });
    });

    const renderOrders = () => {
      return render(
        <BrowserRouter>
          <Orders />
        </BrowserRouter>
      );
    };

    test('should render orders page', async () => {
      renderOrders();

      await waitFor(() => {
        expect(screen.getByText('My Orders')).toBeInTheDocument();
        expect(screen.getByText('Track and manage your orders')).toBeInTheDocument();
      });
    });

    test('should load and display orders', async () => {
      renderOrders();

      await waitFor(() => {
        expect(screen.getByText('ORD-**********-000001')).toBeInTheDocument();
        expect(screen.getByText('Test Sneakers')).toBeInTheDocument();
        expect(screen.getByText('Shipped')).toBeInTheDocument();
      });

      expect(ShippingService.getOrders).toHaveBeenCalledWith('buyer', null, 1, 10);
    });

    test('should switch between buyer and seller tabs', async () => {
      renderOrders();

      await waitFor(() => {
        const sellerTab = screen.getByText('My Sales');
        fireEvent.click(sellerTab);
      });

      expect(ShippingService.getOrders).toHaveBeenCalledWith('seller', null, 1, 10);
    });

    test('should filter orders by status', async () => {
      renderOrders();

      await waitFor(() => {
        const statusFilter = screen.getByDisplayValue('All Status');
        fireEvent.change(statusFilter, { target: { value: 'shipped' } });
      });

      expect(ShippingService.getOrders).toHaveBeenCalledWith('buyer', 'shipped', 1, 10);
    });

    test('should confirm delivery', async () => {
      ShippingService.confirmDelivery.mockResolvedValue({
        success: true,
        data: { order: { ...mockOrders[0], status: 'delivered' } }
      });

      renderOrders();

      await waitFor(() => {
        const confirmButton = screen.getByText('Confirm Delivery');
        fireEvent.click(confirmButton);
      });

      await waitFor(() => {
        expect(ShippingService.confirmDelivery).toHaveBeenCalledWith('order1');
        expect(toast.success).toHaveBeenCalledWith('Delivery confirmed');
      });
    });
  });

  describe('OrderDetails Component', () => {
    const mockOrderDetails = {
      _id: 'order1',
      orderNumber: 'ORD-**********-000001',
      status: 'shipped',
      createdAt: '2024-01-01T10:00:00Z',
      product: {
        title: 'Test Sneakers',
        price: 150,
        product_photos: ['sneaker1.jpg'],
        brand: 'Nike',
        size: '42',
        condition: 'New'
      },
      buyer: {
        username: 'testbuyer',
        profile_picture: 'buyer.jpg'
      },
      seller: {
        username: 'testseller',
        profile_picture: 'seller.jpg'
      },
      orderDetails: {
        productPrice: 150,
        quantity: 1
      },
      payment: {
        fees: {
          platformFee: 15,
          shippingFee: 5,
          tax: 0.72,
          total: 170.72
        }
      },
      shipping: {
        trackingNumber: 'TRACK123',
        estimatedDelivery: '2024-01-05T10:00:00Z',
        toAddress: {
          fullName: 'Test Buyer',
          addressLine1: 'Dubai Mall',
          city: 'Dubai',
          country: 'United Arab Emirates'
        }
      },
      timeline: [
        {
          status: 'shipped',
          timestamp: '2024-01-02T10:00:00Z',
          description: 'Package shipped',
          updatedBy: 'seller'
        }
      ]
    };

    const mockShipment = {
      trackingNumber: 'TRACK123',
      status: 'in_transit',
      events: [
        {
          timestamp: '2024-01-02T10:00:00Z',
          status: 'shipped',
          description: 'Package shipped from origin',
          location: { city: 'Dubai', country: 'AE' }
        },
        {
          timestamp: '2024-01-03T14:00:00Z',
          status: 'in_transit',
          description: 'Package in transit',
          location: { city: 'Abu Dhabi', country: 'AE' }
        }
      ],
      provider: {
        displayName: 'Aramex'
      }
    };

    beforeEach(() => {
      ShippingService.getOrderDetails.mockResolvedValue({
        success: true,
        data: {
          order: mockOrderDetails,
          shipment: mockShipment
        }
      });
    });

    const renderOrderDetails = () => {
      return render(
        <BrowserRouter>
          <OrderDetails />
        </BrowserRouter>
      );
    };

    test('should render order details page', async () => {
      renderOrderDetails();

      await waitFor(() => {
        expect(screen.getByText('Order #ORD-**********-000001')).toBeInTheDocument();
        expect(screen.getByText('Test Sneakers')).toBeInTheDocument();
      });
    });

    test('should display tracking information', async () => {
      renderOrderDetails();

      await waitFor(() => {
        expect(screen.getByText('Tracking Information')).toBeInTheDocument();
        expect(screen.getByText('TRACK123')).toBeInTheDocument();
        expect(screen.getByText('Aramex')).toBeInTheDocument();
      });
    });

    test('should show tracking events', async () => {
      renderOrderDetails();

      await waitFor(() => {
        expect(screen.getByText('Package shipped from origin')).toBeInTheDocument();
        expect(screen.getByText('Package in transit')).toBeInTheDocument();
      });
    });

    test('should refresh tracking information', async () => {
      ShippingService.trackShipment.mockResolvedValue({
        success: true,
        data: { tracking: mockShipment }
      });

      renderOrderDetails();

      await waitFor(() => {
        const refreshButton = screen.getByText('Refresh');
        fireEvent.click(refreshButton);
      });

      await waitFor(() => {
        expect(ShippingService.trackShipment).toHaveBeenCalledWith('TRACK123');
        expect(toast.success).toHaveBeenCalledWith('Tracking information updated');
      });
    });
  });

  describe('TrackingWidget Component', () => {
    const mockTracking = {
      trackingNumber: 'TRACK123',
      status: 'in_transit',
      events: [
        {
          timestamp: '2024-01-02T10:00:00Z',
          status: 'shipped',
          description: 'Package shipped'
        }
      ],
      estimatedDelivery: '2024-01-05T10:00:00Z'
    };

    beforeEach(() => {
      ShippingService.trackShipment.mockResolvedValue({
        success: true,
        data: { tracking: mockTracking }
      });
    });

    test('should render tracking widget', async () => {
      render(<TrackingWidget trackingNumber="TRACK123" />);

      await waitFor(() => {
        expect(screen.getByText('Package Tracking')).toBeInTheDocument();
        expect(screen.getByText('TRACK123')).toBeInTheDocument();
      });
    });

    test('should display tracking status', async () => {
      render(<TrackingWidget trackingNumber="TRACK123" />);

      await waitFor(() => {
        expect(screen.getByText('In Transit')).toBeInTheDocument();
        expect(screen.getByText('Package shipped')).toBeInTheDocument();
      });
    });

    test('should render compact version', async () => {
      render(<TrackingWidget trackingNumber="TRACK123" compact={true} />);

      await waitFor(() => {
        expect(screen.getByText('In Transit')).toBeInTheDocument();
      });

      // Should not show full tracking details in compact mode
      expect(screen.queryByText('Package Tracking')).not.toBeInTheDocument();
    });
  });

  describe('DeliveryProgress Component', () => {
    const mockEvents = [
      {
        timestamp: '2024-01-01T10:00:00Z',
        status: 'created',
        description: 'Order created'
      },
      {
        timestamp: '2024-01-02T10:00:00Z',
        status: 'shipped',
        description: 'Package shipped'
      }
    ];

    test('should render delivery progress', () => {
      render(
        <DeliveryProgress 
          status="shipped" 
          events={mockEvents}
          estimatedDelivery="2024-01-05T10:00:00Z"
        />
      );

      expect(screen.getByText('Delivery Progress')).toBeInTheDocument();
      expect(screen.getByText('Order Placed')).toBeInTheDocument();
      expect(screen.getByText('Picked Up')).toBeInTheDocument();
    });

    test('should show progress bar', () => {
      render(
        <DeliveryProgress 
          status="in_transit" 
          events={mockEvents}
        />
      );

      const progressBar = screen.getByRole('progressbar', { hidden: true });
      expect(progressBar).toBeInTheDocument();
    });

    test('should handle delivered status', () => {
      render(
        <DeliveryProgress 
          status="delivered" 
          events={mockEvents}
          actualDelivery="2024-01-04T15:00:00Z"
        />
      );

      expect(screen.getByText('Delivered')).toBeInTheDocument();
    });
  });

  describe('ShippingService API', () => {
    test('should format currency correctly', () => {
      const formatted = ShippingService.formatCurrency(25.50, 'AED');
      expect(formatted).toBe('AED 25.50');
    });

    test('should format provider names', () => {
      expect(ShippingService.formatProviderName('aramex')).toBe('Aramex');
      expect(ShippingService.formatProviderName('local_pickup')).toBe('Local Pickup');
    });

    test('should format delivery status', () => {
      expect(ShippingService.formatDeliveryStatus('in_transit')).toBe('In Transit');
      expect(ShippingService.formatDeliveryStatus('out_for_delivery')).toBe('Out for Delivery');
    });

    test('should get status colors', () => {
      expect(ShippingService.getStatusColor('delivered')).toBe('text-green-600');
      expect(ShippingService.getStatusColor('cancelled')).toBe('text-red-600');
    });

    test('should calculate estimated delivery', () => {
      const estimated = ShippingService.calculateEstimatedDelivery({ min: 2, max: 5 });
      expect(estimated).toBeInstanceOf(Date);
      
      const daysDiff = Math.ceil((estimated - new Date()) / (1000 * 60 * 60 * 24));
      expect(daysDiff).toBe(5); // Should use max days
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      ShippingService.getOrders.mockRejectedValue({
        error: 'Network error'
      });

      render(
        <BrowserRouter>
          <Orders />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Failed to load orders');
      });
    });

    test('should handle missing tracking data', async () => {
      ShippingService.trackShipment.mockResolvedValue({
        success: false,
        error: 'Tracking not found'
      });

      render(<TrackingWidget trackingNumber="INVALID" />);

      await waitFor(() => {
        expect(screen.getByText('Tracking Error')).toBeInTheDocument();
      });
    });

    test('should handle empty orders list', async () => {
      ShippingService.getOrders.mockResolvedValue({
        success: true,
        data: {
          orders: [],
          pagination: {
            currentPage: 1,
            totalPages: 0,
            totalOrders: 0,
            hasNext: false,
            hasPrev: false
          }
        }
      });

      render(
        <BrowserRouter>
          <Orders />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText('No orders found')).toBeInTheDocument();
      });
    });
  });
});
