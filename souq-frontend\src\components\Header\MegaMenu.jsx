import React, { useRef, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setCategory, setSubcategory, setItem } from '../../redux/slices/CategorySlice';
import { useAppContext } from '../../context/AppContext';
import { categories } from '../../data/categories';

import {
  Folder,
  ShoppingBag,
  Gift,
  User,
  Star,
  Scissors,
  Home,
  Palette,
  Package,
  Zap,
} from 'lucide-react';

const subcategoryIcons = {
  // Women
  'women-clothing': <ShoppingBag size={20} />,
  'women-shoes': <ShoppingBag size={20} />,
  'women-accessories': <Gift size={20} />,
  'women-beauty': <Palette size={20} />,

  // Men
  'men-clothing': <ShoppingBag size={20} />,
  'men-shoes': <ShoppingBag size={20} />,
  'men-accessories': <Gift size={20} />,
  'men-grooming': <Scissors size={20} />,

  // Kids
  'girls': <User size={20} />,
  'boys': <User size={20} />,
  'baby': <User size={20} />,
  'toys': <Package size={20} />,

  // Home
  'home-textiles': <Home size={20} />,
  'home-decor': <Home size={20} />,
  'home-furniture': <Home size={20} />,
  'home-kitchen': <Home size={20} />,

  // Beauty
  'beauty-makeup': <Palette size={20} />,
  'beauty-skin': <Zap size={20} />,
  'beauty-hair': <Scissors size={20} />,
  'beauty-fragrance': <Star size={20} />,

  // Default
  default: <Folder size={20} />,
};

const MegaMenu = () => {
  const dispatch = useDispatch();
  const { isMegaMenuOpen, setIsMegaMenuOpen, activeCategory } = useAppContext();
  const megaMenuRef = useRef(null);
  const [hoveredSubcat, setHoveredSubcat] = useState(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target)) {
        setIsMegaMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setIsMegaMenuOpen]);

  // Set selected category in Redux when it changes
  // useEffect(() => {
  //   if (activeCategory) {
  //     const selectedCategory = categories.find(cat => cat.id === activeCategory);
  //     if (selectedCategory) {
  //       dispatch(setCategory(selectedCategory));
  //     }
  //   }
  // }, [activeCategory, dispatch]);

  const activeMenu = categories.find(cat => cat.id === activeCategory);
  const subcategories = activeMenu?.subcategories || [];
  const currentSubcategory = subcategories.find(sc => sc.id === hoveredSubcat) || subcategories[0];

  if (!isMegaMenuOpen || !activeMenu) return null;

  return (
    <div
      ref={megaMenuRef}
      className="absolute left-0 w-full bg-white shadow-lg border-t border-gray-100 z-50"
      onMouseLeave={() => {
        setHoveredSubcat(null);
        setIsMegaMenuOpen(false);
      }}
    >
      <div className="container mx-auto px-4 py-6">
        <div className="flex">
          {/* LEFT: Subcategory List */}
          <div className="w-1/4 pr-6 ltr:border-r rtl:border-l">
            <ul className="space-y-2">
              {subcategories.map((subcategory) => (
                <li
                  key={subcategory.id}
                  onMouseEnter={() => {
                    setHoveredSubcat(subcategory.id);
                  }}
                  onClick={() => {
                    setHoveredSubcat(subcategory.id);
                    dispatch(setSubcategory(subcategory));
                  }}
                  className={`cursor-pointer px-3 py-2 rounded-md transition-colors ${hoveredSubcat === subcategory.id
                    ? 'bg-gray-100 text-teal-600 font-semibold'
                    : 'text-gray-700 hover:bg-gray-50'
                    }`}
                >
                  <span className="flex items-center gap-2">
                    <span className="text-teal-600">
                      {subcategoryIcons[subcategory.id] || subcategoryIcons.default}
                    </span>
                    {subcategory.name}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* RIGHT: Items under current subcategory */}
          <div className="w-3/6 pl-6 rtl:pr-6">
            <div className="grid grid-cols-2 gap-1">
              {currentSubcategory?.items?.map((item) => (
                <div
                  key={item.id}
                  className="p-2 hover:bg-gray-100 rounded-md transition-colors cursor-pointer"
                  onClick={() => {
                    dispatch(setItem(item));
                    setIsMegaMenuOpen(false); // Close menu on item click
                  }}
                >
                  <p className="text-gray-700 hover:text-teal-600 hover:font-semibold transition-colors">
                    {item.name}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MegaMenu;
