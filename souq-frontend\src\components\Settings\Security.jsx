import React from 'react'
import { FaChevronRight } from 'react-icons/fa6'
import { useNavigate } from 'react-router-dom'

const Security = () => {
    const navigate = useNavigate()
    const user = JSON.parse(localStorage.getItem("user"));
    const email = user?.email;
    
    return (
        <div>
            <h2 className="text-xl font-semibold">Keep your account secure</h2>
            <span className='text-sm text-gray-500'>Review your info to help protect your account.</span>
            <div className="border p-4 rounded-md shadow-sm cursor-pointer mb-3 mt-3" onClick={() => navigate("/email-change", {
                state: {
                    email: email
                }
            })}>
                <div className="flex justify-between items-center">
                    <div>
                        <p className="font-medium">Email</p>
                        <p className="text-sm text-gray-500 mt-1">
                            Keep your email up to date.
                        </p>
                    </div>
                    <FaChevronRight className="text-gray-500 text-lg" />
                </div>
            </div>
            <div className="border p-4 rounded-md shadow-sm cursor-pointer mb-3" onClick={() => navigate("/reset-password")}>
                <div className="flex justify-between items-center">
                    <div>
                        <p className="font-medium">Password</p>
                        <p className="text-sm text-gray-500 mt-1">
                            Protect your account with a stronger password.
                        </p>
                    </div>
                    <FaChevronRight className="text-gray-500 text-lg" />
                </div>
            </div>
            <div className="border p-4 rounded-md shadow-sm cursor-pointer mb-3" onClick={() => navigate("/send-phone-otp")}>
                <div className="flex justify-between items-center">
                    <div>
                        <p className="font-medium">2-step verification</p>
                        <p className="text-sm text-gray-500 mt-1">
                            Confirm new logins with a 4-digit code.
                        </p>
                    </div>
                    <FaChevronRight className="text-gray-500 text-lg" />
                </div>
            </div>
            <div className="border p-4 rounded-md shadow-sm cursor-pointer mb-3" onClick={() => navigate("/login-activity")}>
                <div className="flex justify-between items-center">
                    <div>
                        <p className="font-medium">Login activity</p>
                        <p className="text-sm text-gray-500 mt-1">
                            Manage your logged-in devices.
                        </p>
                    </div>
                    <FaChevronRight className="text-gray-500 text-lg" />
                </div>
            </div>
        </div>
    )
}

export default Security