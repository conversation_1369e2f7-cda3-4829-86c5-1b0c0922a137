# Wallet Currency Functionality

## Overview
Added functionality to change the default wallet currency from USD to AED and allow users to select their preferred currency for wallet display.

## Changes Made

### 1. Backend Changes

#### Database Model Updates (`souq-backend/db/models/walletModel.js`)
- **Changed default primary currency** from `'USD'` to `'AED'`
- **Updated transaction currency default** from `'USD'` to `'AED'`

```javascript
// Primary currency for the wallet
primaryCurrency: {
  type: String,
  default: 'AED', // Changed from 'USD'
  enum: ['USD', 'AED', 'EUR', 'GBP']
},

// Transaction currency
currency: {
  type: String,
  required: true,
  default: 'AED', // Changed from 'USD'
  enum: ['USD', 'AED', 'EUR', 'GBP']
},
```

#### API Endpoint
- **Existing endpoint**: `PUT /api/user/wallet/settings`
- **Functionality**: Update primary currency and withdrawal limits
- **Usage**: `{ "primaryCurrency": "AED" }`

### 2. Frontend Changes

#### Wallet Component (`souq-frontend/src/components/Settings/Wallet.jsx`)
- **Added currency selector state**: `selectedCurrency` with default 'AED'
- **Added currency change handler**: `handleCurrencyChange()`
- **Added currency selector UI**: Button group for USD, AED, EUR, GBP
- **Updated withdrawal form**: Uses selected currency instead of hardcoded USD

#### Currency Selector UI
```jsx
<div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
  <div className="flex items-center justify-between">
    <div>
      <h3 className="text-sm font-medium text-gray-900">Primary Currency</h3>
      <p className="text-xs text-gray-500">Choose your preferred currency for wallet display</p>
    </div>
    <div className="flex items-center space-x-2">
      {['USD', 'AED', 'EUR', 'GBP'].map((currency) => (
        <button
          key={currency}
          onClick={() => handleCurrencyChange(currency)}
          className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
            selectedCurrency === currency
              ? 'bg-teal-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          {currency}
        </button>
      ))}
    </div>
  </div>
</div>
```

#### Other Component Updates
- **WalletIndicator**: Default currency changed to AED
- **WalletBalance**: Default fallback currency changed to AED
- **WalletService**: Default currency parameters changed to AED

### 3. API Service Updates (`souq-frontend/src/api/WalletService.js`)
- **getWalletBalance**: Default currency changed from 'USD' to 'AED'
- **formatCurrency**: Default currency changed from 'USD' to 'AED'

## Features

### 1. Currency Selection
- **Visual Selector**: Button group showing USD, AED, EUR, GBP
- **Active State**: Selected currency highlighted in teal
- **Real-time Update**: Changes saved immediately to backend
- **Success Feedback**: Toast notification on successful update

### 2. Persistent Settings
- **Database Storage**: Primary currency saved in user's wallet settings
- **Auto-load**: Selected currency loads from database on page refresh
- **Consistent Display**: All wallet amounts show in selected currency

### 3. Multi-currency Support
- **Balance Display**: Shows balances in all supported currencies
- **Transaction History**: Maintains original transaction currencies
- **Withdrawal**: Uses selected currency as default for new withdrawals

## Usage

### For Users
1. **Navigate to Wallet page** (`/wallet`)
2. **See currency selector** at the top of the page
3. **Click desired currency** (USD, AED, EUR, GBP)
4. **Confirmation**: Toast message confirms the change
5. **Immediate Effect**: All amounts update to show in selected currency

### For Developers
```javascript
// Update wallet currency
const response = await updateWalletSettings({
  primaryCurrency: 'AED'
});

// Get balance in specific currency
const balance = await getWalletBalance('AED');

// Format currency with default AED
const formatted = formatCurrency(100); // Returns "AED 100.00"
```

## API Endpoints

### Update Wallet Settings
```
PUT /api/user/wallet/settings
Content-Type: application/json
Authorization: Bearer <token>

{
  "primaryCurrency": "AED"
}
```

### Get Wallet Balance
```
GET /api/user/wallet/balance?currency=AED
Authorization: Bearer <token>
```

## Default Behavior

### New Users
- **Default Currency**: AED
- **Wallet Creation**: New wallets created with AED as primary currency
- **Initial Display**: All amounts show in AED by default

### Existing Users
- **Backward Compatibility**: Existing USD wallets continue to work
- **Manual Change**: Users can change to AED using the currency selector
- **Preserved Settings**: Currency preference saved permanently

## Testing

### Manual Testing
1. **Create new account** → Should default to AED
2. **Change currency** → Should update immediately
3. **Refresh page** → Should remember selected currency
4. **Check all wallet displays** → Should show in selected currency
5. **Make withdrawal** → Should default to selected currency

### API Testing
```bash
# Update to AED
curl -X PUT "http://localhost:5000/api/user/wallet/settings" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"primaryCurrency": "AED"}'

# Get AED balance
curl -X GET "http://localhost:5000/api/user/wallet/balance?currency=AED" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Files Modified

### Backend
- `souq-backend/db/models/walletModel.js` - Updated default currencies

### Frontend
- `souq-frontend/src/components/Settings/Wallet.jsx` - Added currency selector
- `souq-frontend/src/components/Header/WalletIndicator.jsx` - Updated default
- `souq-frontend/src/components/Wallet/WalletBalance.jsx` - Updated default
- `souq-frontend/src/api/WalletService.js` - Updated default parameters

## Benefits
- ✅ **AED as Default**: New users see AED currency by default
- ✅ **User Choice**: Users can select preferred currency
- ✅ **Persistent Settings**: Currency preference saved permanently
- ✅ **Real-time Updates**: Changes apply immediately
- ✅ **Multi-currency Support**: Supports USD, AED, EUR, GBP
- ✅ **Backward Compatible**: Existing USD wallets continue to work
