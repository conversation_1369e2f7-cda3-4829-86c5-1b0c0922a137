import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_ADMIN_API_URL || 'http://localhost:5001/api/admin';

// Create axios instance
const adminApiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Network error handler function (will be set by components)
let networkErrorHandler = null;

// Function to set network error handler
export const setNetworkErrorHandler = (handler) => {
  networkErrorHandler = handler;
};

// Request interceptor to add auth token
adminApiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('adminAccessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh and network errors
adminApiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle network errors first
    if (networkErrorHandler) {
      const isNetworkError =
        !navigator.onLine ||
        error.code === 'NETWORK_ERROR' ||
        error.code === 'ERR_NETWORK' ||
        error.message?.toLowerCase().includes('network') ||
        error.message?.toLowerCase().includes('timeout') ||
        error.name === 'NetworkError' ||
        error.name === 'TypeError' ||
        (error.response && error.response.status >= 500) ||
        (error.response && error.response.status === 0);

      if (isNetworkError) {
        // Let the network error handler deal with it
        networkErrorHandler(error);
        return Promise.reject(error);
      }
    }

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('adminRefreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {
            refreshToken
          });

          if (response.data.success) {
            const { accessToken, refreshToken: newRefreshToken } = response.data.data;
            localStorage.setItem('adminAccessToken', accessToken);
            localStorage.setItem('adminRefreshToken', newRefreshToken);
            
            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return adminApiClient(originalRequest);
          }
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('adminAccessToken');
        localStorage.removeItem('adminRefreshToken');
        window.location.href = '/admin/login';
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const adminApi = {
  // Authentication
  login: async (email, password) => {
    const response = await adminApiClient.post('/auth/login', { email, password });
    return response.data;
  },

  signup: async (firstName, lastName, email, password, role) => {
    const response = await adminApiClient.post('/auth/signup', {
      firstName,
      lastName,
      email,
      password,
      role
    });
    return response.data;
  },

  logout: async () => {
    const response = await adminApiClient.post('/auth/logout');
    return response.data;
  },

  refreshToken: async (refreshToken) => {
    const response = await adminApiClient.post('/auth/refresh-token', { refreshToken });
    return response.data;
  },

  getProfile: async () => {
    const response = await adminApiClient.get('/auth/profile');
    return response.data;
  },

  updateProfile: async (profileData) => {
    const response = await adminApiClient.put('/auth/profile', profileData);
    return response.data;
  },

  changePassword: async (currentPassword, newPassword) => {
    const response = await adminApiClient.put('/auth/change-password', {
      currentPassword,
      newPassword
    });
    return response.data;
  },

  // User Management
  getUsers: async (params = {}) => {
    const response = await adminApiClient.get('/users', { params });
    return response.data;
  },

  getUserById: async (userId) => {
    const response = await adminApiClient.get(`/users/${userId}`);
    return response.data;
  },

  updateUser: async (userId, userData) => {
    const response = await adminApiClient.put(`/users/${userId}`, userData);
    return response.data;
  },

  suspendUser: async (userId, reason) => {
    const response = await adminApiClient.post(`/users/${userId}/suspend`, { reason });
    return response.data;
  },

  reactivateUser: async (userId) => {
    const response = await adminApiClient.post(`/users/${userId}/reactivate`);
    return response.data;
  },

  deleteUser: async (userId) => {
    const response = await adminApiClient.delete(`/users/${userId}`);
    return response.data;
  },

  getUserStats: async () => {
    const response = await adminApiClient.get('/users/stats');
    return response.data;
  },

  // Listing Management
  getListings: async (params = {}) => {
    const response = await adminApiClient.get('/listings', { params });
    return response.data;
  },

  getListingById: async (listingId) => {
    const response = await adminApiClient.get(`/listings/${listingId}`);
    return response.data;
  },

  updateListing: async (listingId, listingData) => {
    const response = await adminApiClient.put(`/listings/${listingId}`, listingData);
    return response.data;
  },

  updateListingStatus: async (listingId, status) => {
    const response = await adminApiClient.put(`/listings/${listingId}/status`, { status });
    return response.data;
  },

  approveListing: async (listingId, notes) => {
    const response = await adminApiClient.post(`/listings/${listingId}/approve`, { notes });
    return response.data;
  },

  rejectListing: async (listingId, reason, notes) => {
    const response = await adminApiClient.post(`/listings/${listingId}/reject`, { reason, notes });
    return response.data;
  },

  suspendListing: async (listingId, reason, notes) => {
    const response = await adminApiClient.post(`/listings/${listingId}/suspend`, { reason, notes });
    return response.data;
  },

  deleteListing: async (listingId) => {
    const response = await adminApiClient.delete(`/listings/${listingId}`);
    return response.data;
  },

  getListingStats: async () => {
    const response = await adminApiClient.get('/listings/stats');
    return response.data;
  },

  bulkListingActions: async (action, listingIds, reason, notes) => {
    const response = await adminApiClient.post('/listings/bulk-actions', {
      action,
      listingIds,
      reason,
      notes
    });
    return response.data;
  },

  // Dispute Management
  getDisputes: async (params = {}) => {
    const response = await adminApiClient.get('/disputes', { params });
    return response.data;
  },

  getDisputeById: async (disputeId) => {
    const response = await adminApiClient.get(`/disputes/${disputeId}`);
    return response.data;
  },

  assignDispute: async (disputeId, adminId) => {
    const response = await adminApiClient.post(`/disputes/${disputeId}/assign`, { adminId });
    return response.data;
  },

  addDisputeMessage: async (disputeId, message, isInternal) => {
    const response = await adminApiClient.post(`/disputes/${disputeId}/messages`, {
      message,
      isInternal
    });
    return response.data;
  },

  updateDisputePriority: async (disputeId, priority) => {
    const response = await adminApiClient.put(`/disputes/${disputeId}/priority`, { priority });
    return response.data;
  },

  resolveDispute: async (disputeId, resolution, resolutionDetails, refundAmount) => {
    const response = await adminApiClient.post(`/disputes/${disputeId}/resolve`, {
      resolution,
      resolutionDetails,
      refundAmount
    });
    return response.data;
  },

  escalateDispute: async (disputeId, reason) => {
    const response = await adminApiClient.post(`/disputes/${disputeId}/escalate`, { reason });
    return response.data;
  },

  closeDispute: async (disputeId, notes) => {
    const response = await adminApiClient.post(`/disputes/${disputeId}/close`, { notes });
    return response.data;
  },

  getDisputeStats: async () => {
    const response = await adminApiClient.get('/disputes/stats');
    return response.data;
  },

  // Counterfeit Management
  getCounterfeitFlags: async (params = {}) => {
    const response = await adminApiClient.get('/counterfeit', { params });
    return response.data;
  },

  getCounterfeitFlagById: async (flagId) => {
    const response = await adminApiClient.get(`/counterfeit/${flagId}`);
    return response.data;
  },

  assignInvestigation: async (flagId, adminId) => {
    const response = await adminApiClient.post(`/counterfeit/${flagId}/assign`, { adminId });
    return response.data;
  },

  addInvestigationNote: async (flagId, note, isInternal) => {
    const response = await adminApiClient.post(`/counterfeit/${flagId}/notes`, {
      note,
      isInternal
    });
    return response.data;
  },

  updateFlagPriority: async (flagId, priority) => {
    const response = await adminApiClient.put(`/counterfeit/${flagId}/priority`, { priority });
    return response.data;
  },

  completeInvestigation: async (flagId, verdict, verdictReason) => {
    const response = await adminApiClient.post(`/counterfeit/${flagId}/complete`, {
      verdict,
      verdictReason
    });
    return response.data;
  },

  takeCounterfeitAction: async (flagId, actionType, description) => {
    const response = await adminApiClient.post(`/counterfeit/${flagId}/action`, {
      actionType,
      description
    });
    return response.data;
  },

  dismissFlag: async (flagId, reason) => {
    const response = await adminApiClient.post(`/counterfeit/${flagId}/dismiss`, { reason });
    return response.data;
  },

  getCounterfeitStats: async () => {
    const response = await adminApiClient.get('/counterfeit/stats');
    return response.data;
  },

  // Analytics
  getDashboardStats: async () => {
    const response = await adminApiClient.get('/analytics/dashboard');
    return response.data;
  },

  getSalesAnalytics: async (params = {}) => {
    const response = await adminApiClient.get('/analytics/sales', { params });
    return response.data;
  },

  getTopSellers: async (params = {}) => {
    const response = await adminApiClient.get('/analytics/top-sellers', { params });
    return response.data;
  },

  getCategoryTrends: async (params = {}) => {
    const response = await adminApiClient.get('/analytics/category-trends', { params });
    return response.data;
  },

  getUserAnalytics: async () => {
    const response = await adminApiClient.get('/analytics/users');
    return response.data;
  },

  // Categories
  getCategories: async () => {
    const response = await adminApiClient.get('/categories');
    return response.data;
  },

  createCategory: async (categoryData) => {
    const response = await adminApiClient.post('/categories', categoryData);
    return response.data;
  },

  updateCategory: async (categoryId, categoryData) => {
    const response = await adminApiClient.put(`/categories/${categoryId}`, categoryData);
    return response.data;
  },

  deleteCategory: async (categoryId) => {
    const response = await adminApiClient.delete(`/categories/${categoryId}`);
    return response.data;
  },

  // Subcategories
  addSubCategory: async (categoryId, subCategoryData) => {
    const response = await adminApiClient.post(`/categories/${categoryId}/subcategory`, subCategoryData);
    return response.data;
  },

  updateSubCategory: async (subCategoryId, subCategoryData) => {
    const response = await adminApiClient.put(`/categories/subcategory/${subCategoryId}`, subCategoryData);
    return response.data;
  },

  deleteSubCategory: async (subCategoryId) => {
    const response = await adminApiClient.delete(`/categories/subcategory/${subCategoryId}`);
    return response.data;
  },

  // Menus
  getMenus: async () => {
    const response = await adminApiClient.get('/menus');
    return response.data;
  },

  createMenu: async (menuData) => {
    const response = await adminApiClient.post('/menus', menuData);
    return response.data;
  },

  updateMenu: async (menuId, menuData) => {
    const response = await adminApiClient.put(`/menus/${menuId}`, menuData);
    return response.data;
  },

  deleteMenu: async (menuId) => {
    const response = await adminApiClient.delete(`/menus/${menuId}`);
    return response.data;
  },

  getMenuStats: async () => {
    const response = await adminApiClient.get('/menus/stats');
    return response.data;
  },

  // Size Charts
  getSizeCharts: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await adminApiClient.get(`/size${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  getSizeChartById: async (id) => {
    const response = await adminApiClient.get(`/size/${id}`);
    return response.data;
  },

  createSizeChart: async (sizeChartData) => {
    const response = await adminApiClient.post('/size', sizeChartData);
    return response.data;
  },

  updateSizeChart: async (id, sizeChartData) => {
    const response = await adminApiClient.put(`/size/${id}`, sizeChartData);
    return response.data;
  },

  deleteSizeChart: async (id) => {
    const response = await adminApiClient.delete(`/size/${id}`);
    return response.data;
  },

  getChildCategories: async () => {
    const response = await adminApiClient.get('/size/child-categories');
    return response.data;
  },

  getSizesByChildCategory: async (childCategoryId, slug) => {
    const response = await adminApiClient.get(`/size/category/${childCategoryId}/${slug}`);
    return response.data;
  },

  // Location Management - Countries
  getCountries: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await adminApiClient.get(`/locations/countries${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  getCountryById: async (id) => {
    const response = await adminApiClient.get(`/locations/countries/${id}`);
    return response.data;
  },

  createCountry: async (countryData) => {
    const response = await adminApiClient.post('/locations/countries', countryData);
    return response.data;
  },

  updateCountry: async (id, countryData) => {
    const response = await adminApiClient.put(`/locations/countries/${id}`, countryData);
    return response.data;
  },

  deleteCountry: async (id) => {
    const response = await adminApiClient.delete(`/locations/countries/${id}`);
    return response.data;
  },

  getCountryStats: async () => {
    const response = await adminApiClient.get('/locations/countries/stats');
    return response.data;
  },

  // Location Management - Cities
  getCities: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await adminApiClient.get(`/locations/cities${queryString ? `?${queryString}` : ''}`);
    return response.data;
  },

  getCityById: async (id) => {
    const response = await adminApiClient.get(`/locations/cities/${id}`);
    return response.data;
  },

  createCity: async (cityData) => {
    const response = await adminApiClient.post('/locations/cities', cityData);
    return response.data;
  },

  updateCity: async (id, cityData) => {
    const response = await adminApiClient.put(`/locations/cities/${id}`, cityData);
    return response.data;
  },

  deleteCity: async (id) => {
    const response = await adminApiClient.delete(`/locations/cities/${id}`);
    return response.data;
  },

  getCityStats: async () => {
    const response = await adminApiClient.get('/locations/cities/stats');
    return response.data;
  }
};

export default adminApiClient;
