const axios = require('axios');

const API_BASE_URL = 'http://localhost:5001/api/admin';

// Test function to check if location endpoints are working
async function testLocationAPI() {
    try {
        console.log('🧪 Testing Location API endpoints...');
        
        // Test countries endpoint
        console.log('\n📍 Testing GET /countries...');
        const countriesResponse = await axios.get(`${API_BASE_URL}/locations/countries`, {
            headers: {
                'Authorization': 'Bearer test-token' // This will fail auth but should show the endpoint exists
            }
        });
        console.log('✅ Countries endpoint response:', countriesResponse.status);
        
    } catch (error) {
        if (error.response) {
            console.log(`📡 Countries endpoint exists (Status: ${error.response.status})`);
            if (error.response.status === 401) {
                console.log('🔐 Authentication required (expected)');
            }
        } else {
            console.error('❌ Countries endpoint error:', error.message);
        }
    }
    
    try {
        // Test cities endpoint
        console.log('\n🏙️ Testing GET /cities...');
        const citiesResponse = await axios.get(`${API_BASE_URL}/locations/cities`, {
            headers: {
                'Authorization': 'Bearer test-token'
            }
        });
        console.log('✅ Cities endpoint response:', citiesResponse.status);
        
    } catch (error) {
        if (error.response) {
            console.log(`📡 Cities endpoint exists (Status: ${error.response.status})`);
            if (error.response.status === 401) {
                console.log('🔐 Authentication required (expected)');
            }
        } else {
            console.error('❌ Cities endpoint error:', error.message);
        }
    }
    
    // Test if admin routes are mounted
    try {
        console.log('\n🔍 Testing admin base endpoint...');
        const adminResponse = await axios.get(`${API_BASE_URL}/auth/profile`, {
            headers: {
                'Authorization': 'Bearer test-token'
            }
        });
        
    } catch (error) {
        if (error.response) {
            console.log(`📡 Admin API is mounted (Status: ${error.response.status})`);
        } else {
            console.error('❌ Admin API error:', error.message);
        }
    }
}

testLocationAPI();
