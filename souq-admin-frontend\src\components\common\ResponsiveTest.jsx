import React from 'react';
import { 
  Container, 
  ResponsiveGrid, 
  ResponsiveCard, 
  ResponsiveButton,
  ResponsiveStatsCard,
  ResponsiveFlex,
  ResponsiveTable 
} from './ResponsiveLayout';
import { useResponsive } from '../../hooks/useResponsive';

/**
 * Test component to demonstrate responsive functionality
 * This can be used to test responsive behavior across different screen sizes
 */
const ResponsiveTest = () => {
  const { 
    breakpoint, 
    windowSize, 
    isMobile, 
    isTablet, 
    isDesktop,
    isSmUp,
    isMdUp,
    isLgUp 
  } = useResponsive();

  const testData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'Active' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'Inactive' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', status: 'Pending' }
  ];

  return (
    <Container className="py-6">
      {/* Responsive Info Card */}
      <ResponsiveCard className="mb-6 bg-blue-50 border-blue-200">
        <h2 className="text-lg sm:text-xl font-semibold mb-4 text-blue-900">
          Responsive Test Component
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div>
            <strong>Current Breakpoint:</strong> {breakpoint}
          </div>
          <div>
            <strong>Window Size:</strong> {windowSize.width}x{windowSize.height}
          </div>
          <div>
            <strong>Device Type:</strong> {isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}
          </div>
          <div>
            <strong>Responsive Flags:</strong>
            <div className="mt-1 space-y-1">
              <div>SM+: {isSmUp ? '✅' : '❌'}</div>
              <div>MD+: {isMdUp ? '✅' : '❌'}</div>
              <div>LG+: {isLgUp ? '✅' : '❌'}</div>
            </div>
          </div>
        </div>
      </ResponsiveCard>

      {/* Stats Grid Test */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Responsive Stats Grid</h3>
        <ResponsiveGrid cols={4} gap="medium">
          <ResponsiveStatsCard
            title="Total Users"
            value="1,234"
            change="+12.5%"
            icon={
              <svg className="w-full h-full text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            }
          />
          <ResponsiveStatsCard
            title="Revenue"
            value="$45,678"
            change="+8.2%"
            icon={
              <svg className="w-full h-full text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            }
          />
          <ResponsiveStatsCard
            title="Orders"
            value="567"
            change="+15.3%"
            icon={
              <svg className="w-full h-full text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
            }
          />
          <ResponsiveStatsCard
            title="Growth"
            value="23.4%"
            change="+5.1%"
            icon={
              <svg className="w-full h-full text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
          />
        </ResponsiveGrid>
      </div>

      {/* Button Test */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Responsive Buttons</h3>
        <ResponsiveFlex gap="medium" className="flex-wrap">
          <ResponsiveButton size="small" variant="primary">
            Small Button
          </ResponsiveButton>
          <ResponsiveButton size="medium" variant="secondary">
            Medium Button
          </ResponsiveButton>
          <ResponsiveButton size="large" variant="outline">
            Large Button
          </ResponsiveButton>
          <ResponsiveButton size="medium" variant="danger">
            Danger Button
          </ResponsiveButton>
        </ResponsiveFlex>
      </div>

      {/* Table Test */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Responsive Table</h3>
        <ResponsiveTable>
          <thead className="bg-gray-50">
            <tr>
              <th className="px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="hidden sm:table-cell px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Email
              </th>
              <th className="px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {testData.map((item) => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-3 py-4 sm:px-6">
                  <div className="text-sm font-medium text-gray-900">{item.name}</div>
                  {/* Show email on mobile */}
                  <div className="sm:hidden text-xs text-gray-500 mt-1">{item.email}</div>
                </td>
                <td className="hidden sm:table-cell px-3 py-4 sm:px-6 text-sm text-gray-500">
                  {item.email}
                </td>
                <td className="px-3 py-4 sm:px-6">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    item.status === 'Active' ? 'bg-green-100 text-green-800' :
                    item.status === 'Inactive' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {item.status}
                  </span>
                </td>
                <td className="px-3 py-4 sm:px-6">
                  <div className="flex flex-col sm:flex-row gap-2">
                    <ResponsiveButton size="small" variant="outline">
                      Edit
                    </ResponsiveButton>
                    <ResponsiveButton size="small" variant="danger">
                      Delete
                    </ResponsiveButton>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </ResponsiveTable>
      </div>

      {/* Grid Test */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Responsive Grid Layouts</h3>
        
        <div className="space-y-6">
          <div>
            <h4 className="text-md font-medium mb-2">2 Column Grid</h4>
            <ResponsiveGrid cols={2} gap="medium">
              <ResponsiveCard>Card 1</ResponsiveCard>
              <ResponsiveCard>Card 2</ResponsiveCard>
            </ResponsiveGrid>
          </div>
          
          <div>
            <h4 className="text-md font-medium mb-2">3 Column Grid</h4>
            <ResponsiveGrid cols={3} gap="medium">
              <ResponsiveCard>Card 1</ResponsiveCard>
              <ResponsiveCard>Card 2</ResponsiveCard>
              <ResponsiveCard>Card 3</ResponsiveCard>
            </ResponsiveGrid>
          </div>
          
          <div>
            <h4 className="text-md font-medium mb-2">4 Column Grid</h4>
            <ResponsiveGrid cols={4} gap="medium">
              <ResponsiveCard>Card 1</ResponsiveCard>
              <ResponsiveCard>Card 2</ResponsiveCard>
              <ResponsiveCard>Card 3</ResponsiveCard>
              <ResponsiveCard>Card 4</ResponsiveCard>
            </ResponsiveGrid>
          </div>
        </div>
      </div>

      {/* Responsive Visibility Test */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Responsive Visibility</h3>
        <div className="space-y-2 text-sm">
          <div className="block sm:hidden p-2 bg-red-100 text-red-800 rounded">
            📱 This is only visible on mobile (xs)
          </div>
          <div className="hidden sm:block md:hidden p-2 bg-blue-100 text-blue-800 rounded">
            📱 This is only visible on small screens (sm)
          </div>
          <div className="hidden md:block lg:hidden p-2 bg-green-100 text-green-800 rounded">
            💻 This is only visible on medium screens (md)
          </div>
          <div className="hidden lg:block xl:hidden p-2 bg-yellow-100 text-yellow-800 rounded">
            🖥️ This is only visible on large screens (lg)
          </div>
          <div className="hidden xl:block p-2 bg-purple-100 text-purple-800 rounded">
            🖥️ This is only visible on extra large screens (xl+)
          </div>
        </div>
      </div>

      {/* Instructions */}
      <ResponsiveCard className="bg-gray-50">
        <h3 className="text-lg font-semibold mb-4">Testing Instructions</h3>
        <div className="text-sm space-y-2">
          <p>🔧 <strong>How to test:</strong></p>
          <ol className="list-decimal list-inside space-y-1 ml-4">
            <li>Open browser developer tools (F12)</li>
            <li>Click the device toolbar icon</li>
            <li>Try different device presets (iPhone, iPad, Desktop)</li>
            <li>Resize the browser window manually</li>
            <li>Watch how components adapt to different screen sizes</li>
          </ol>
          
          <p className="mt-4">📱 <strong>Expected behavior:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Stats grid: 1 col (mobile) → 2 cols (tablet) → 4 cols (desktop)</li>
            <li>Table: Hidden email column on mobile, stacked buttons</li>
            <li>Buttons: Smaller on mobile, larger on desktop</li>
            <li>Cards: Responsive padding and text sizes</li>
            <li>Visibility: Different messages for different screen sizes</li>
          </ul>
        </div>
      </ResponsiveCard>
    </Container>
  );
};

export default ResponsiveTest;
