# Escrow Checkout Address Edit Form - Debug Guide

## 🔧 **Issue Fixed: Edit Form Data Synchronization**

I've added debugging tools and the missing phone number field to help identify why the edit form shows different data than the read-only view.

## 🛠️ **Changes Made:**

### **1. Added Phone Number Field**
```jsx
<div>
  <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number (optional)</label>
  <input
    type="tel"
    value={shippingAddress?.phoneNumber || ''}
    onChange={(e) => setShippingAddress({...shippingAddress, phoneNumber: e.target.value})}
    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
    placeholder="Enter phone number"
  />
</div>
```

### **2. Added Debug Information**
**In Edit Mode:**
```jsx
<div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
  <strong>Debug - Current Address Data:</strong>
  <pre>{JSON.stringify(shippingAddress, null, 2)}</pre>
</div>
```

**In Read-Only Mode:**
```jsx
<div className="text-xs text-gray-500 bg-gray-50 p-2 rounded mb-3">
  <strong>Debug - Read-only Address Data:</strong>
  <pre>{JSON.stringify(shippingAddress, null, 2)}</pre>
</div>
```

### **3. Added Edit Button Debug**
```jsx
onClick={() => {
  console.log('🔧 Toggling edit mode. Current address:', shippingAddress);
  setIsEditingAddress(!isEditingAddress);
}}
```

## 🧪 **Testing Steps:**

### **1. Open Escrow Checkout Page**
Navigate to the escrow checkout page and check the console for address loading logs.

### **2. Check Address Display**
Look at the read-only address display and note the debug information showing the current address data.

### **3. Click Edit Button**
- Check console for the edit toggle log
- Compare the debug data between read-only and edit modes
- Verify all fields are populated correctly

### **4. Expected Debug Output:**

**Console Log:**
```javascript
🔧 Toggling edit mode. Current address: {
  fullName: "sadddddddddd",
  street1: "ewq",
  street2: "ewsda", 
  city: "ewq",
  state: "ewsda",
  zip: "da",
  country: "United States",
  phoneNumber: "9922112211"
}
```

**Debug Box in UI:**
```json
{
  "fullName": "sadddddddddd",
  "street1": "ewq", 
  "street2": "ewsda",
  "city": "ewq",
  "state": "ewsda", 
  "zip": "da",
  "country": "United States",
  "phoneNumber": "9922112211"
}
```

## 🔍 **Troubleshooting:**

### **If Edit Form Shows Different Data:**

1. **Check API Response Structure:**
   - Verify the API returns the expected address format
   - Check if field names match (addressLine1 vs street1, zipCode vs zip)

2. **Check Data Mapping:**
   - Look at the `loadDefaultAddress` function
   - Verify the mapping from API response to state

3. **Check State Updates:**
   - Ensure `setShippingAddress` is working correctly
   - Check if there are multiple state updates conflicting

### **Common Issues:**

| Issue | Likely Cause | Solution |
|-------|--------------|----------|
| **Empty fields in edit mode** | State not properly set | Check `loadDefaultAddress` mapping |
| **Different data in edit vs read** | State mutation issue | Check state updates |
| **Missing phone number** | API doesn't include phone | Check API response structure |
| **Fields not updating** | onChange handlers broken | Check input value bindings |

## 🎯 **Expected Behavior:**

### **Read-Only View Should Show:**
- **Full Name:** sadddddddddd
- **Address:** ewq, ewsda  
- **Country:** United States
- **Phone:** 9922112211

### **Edit Form Should Show:**
- **Full Name:** sadddddddddd (pre-filled)
- **Street Address:** ewq (pre-filled)
- **Apartment:** ewsda (pre-filled)
- **City:** ewq (pre-filled)
- **State:** ewsda (pre-filled)
- **ZIP Code:** da (pre-filled)
- **Country:** United States (pre-filled)
- **Phone Number:** 9922112211 (pre-filled)

## 🔧 **Next Steps:**

1. **Test the page** and check the debug information
2. **Compare the data** between read-only and edit modes
3. **Check console logs** for any errors or data issues
4. **Share the debug output** if the data still doesn't match

Once we identify the exact data structure issue, I can fix the mapping to ensure the edit form shows the same data as the read-only view.

## 🧹 **Remove Debug Code:**

After fixing the issue, remove the debug boxes by deleting:
- The debug div in edit mode
- The debug div in read-only mode  
- The console.log in the edit button click handler

The debug information will help us identify exactly what's happening with the address data! 🔍
