# Orders API Updated to Use Transactions & StandardPayments

## ✅ **Orders API Now Fetches from Transactions & StandardPayments Models**

The `/api/user/orders` endpoint now retrieves data from both `transactions` and `standardpayments` database models instead of the `orders` model.

## 🔧 **Backend Changes Made:**

### **1. Updated Order Controller** (`app/user/shipping/controllers/orderController.js`)

#### **Added Model Imports:**
```javascript
const Transaction = require('../../../../db/models/transactionModel');
const StandardPayment = require('../../../../db/models/standardPaymentModel');
```

#### **Updated getUserOrders() Method:**
- **Fetches from both models** using `Promise.all()`
- **Combines results** from transactions (escrow) and standardpayments
- **Formats unified response** with consistent structure
- **Maintains pagination** across both data sources

#### **Updated getOrderDetails() Method:**
- **Searches both models** for order details
- **Returns formatted order** with payment type information
- **Handles both escrow and standard payment types**

### **2. Data Structure Mapping:**

| Field | Transactions Model | StandardPayments Model |
|-------|-------------------|------------------------|
| **Order ID** | `transactionId` | `transactionId` |
| **Amount** | `amount` | `productPrice` |
| **Status** | `status` | `status` |
| **Payment Gateway** | `paymentGateway` | `paymentGateway` |
| **Shipping Address** | `escrowTransaction.shippingAddress` | `shippingAddress` |
| **Currency** | `currency` | `currency` |

## 🎯 **Frontend Changes Made:**

### **1. Updated Orders Page** (`src/pages/Orders.jsx`)

#### **Added Payment Type Badge:**
- **Escrow orders**: Blue badge "Escrow"
- **Standard orders**: Green badge "Standard"

#### **Added Payment Details Section:**
- **Payment Method**: Escrow or Standard
- **Payment Gateway**: Stripe, PayTabs, etc.
- **Total Amount**: Transaction total
- **Platform Fee**: For standard payments

## 📊 **API Response Structure:**

### **GET /api/user/orders?role=buyer&page=1&limit=10**

```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "_id": "transaction_or_payment_id",
        "orderNumber": "TXN_1234567890_ABC123",
        "type": "escrow", // or "standard"
        "buyer": {
          "_id": "buyer_id",
          "username": "john_doe",
          "profile_picture": "avatar.jpg"
        },
        "seller": {
          "_id": "seller_id", 
          "username": "jane_seller",
          "profile_picture": "seller.jpg"
        },
        "product": {
          "_id": "product_id",
          "title": "Product Name",
          "price": 100,
          "product_photos": ["photo1.jpg"],
          "brand": "Brand Name",
          "size": "M"
        },
        "status": "completed",
        "orderDetails": {
          "productPrice": 100,
          "offerAmount": null,
          "quantity": 1,
          "currency": "USD"
        },
        "payment": {
          "method": "escrow",
          "status": "completed",
          "transactionId": "TXN_1234567890_ABC123",
          "paymentGateway": "stripe",
          "fees": {
            "total": 100
          }
        },
        "shipping": {
          "toAddress": {
            "fullName": "John Doe",
            "street1": "123 Main St",
            "city": "New York",
            "zip": "10001",
            "country": "USA"
          }
        },
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 1,
      "totalOrders": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

## 🧪 **How to Test:**

### **1. Backend Testing:**
```bash
# Start backend server
cd souq-backend
npm start

# Test API endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:5000/api/user/orders?role=buyer&page=1&limit=10"
```

### **2. Frontend Testing:**
1. **Login to your account**
2. **Navigate to Orders page** (`/orders`)
3. **Check for:**
   - Orders from both transactions and standardpayments
   - Payment type badges (Escrow/Standard)
   - Payment details section
   - Proper pagination

### **3. Database Requirements:**
Make sure you have data in either:
- **transactions** collection (for escrow orders)
- **standardpayments** collection (for standard orders)

## 🔍 **Debug Information:**

The API now includes detailed console logging:
```javascript
console.log('🔄 Fetching orders for user:', userId, 'role:', role);
console.log('📦 Found transactions:', transactions.length);
console.log('💳 Found standard payments:', standardPayments.length);
console.log('✅ Total orders found:', totalOrders);
```

## ✅ **Expected Results:**

### **If you have transactions:**
- Orders will show with "Escrow" badge
- Payment method will be "escrow"
- Total amount from transaction.amount

### **If you have standard payments:**
- Orders will show with "Standard" badge  
- Payment method will be "standard"
- Detailed fee breakdown (platform fee, shipping, tax)

### **If you have both:**
- Combined list sorted by creation date
- Proper pagination across both sources
- Distinct visual indicators for each type

## 🚀 **Next Steps:**

1. **Test the updated API** with your existing data
2. **Verify orders display** correctly on frontend
3. **Check pagination** works across both data sources
4. **Confirm payment details** show properly

The Orders page now displays real transaction data from your payment systems! 🎉
