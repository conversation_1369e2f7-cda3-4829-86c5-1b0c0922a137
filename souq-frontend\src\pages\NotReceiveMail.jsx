import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';

const NotReceiveMail = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [email, setEmail] = useState('');

    useEffect(() => {
        if (location?.state?.email) {
            setEmail(location.state.email);
        }
    }, [location]);

    const {
        handleSubmit,
        formState: { isSubmitting },
    } = useForm();

    const onSubmit = async () => {
        try {
            if (!email) return alert("Please enter an email address");

            // Simulate API call delay
            await new Promise((resolve) => setTimeout(resolve, 2000));
            console.log('Verification email resent to:', email);

            navigate('/email-verify', { state: { email } });
        } catch (error) {
            console.error('Resend failed:', error);
        }
    };

    return (
        <div className="min-h-screen bg-gray-100 py-12 px-4">
            <div className="bg-white rounded-xl shadow-md p-8 max-w-md w-full mx-auto">
                <h1 className="text-2xl font-semibold mb-4 text-center">Didn’t Receive Email?</h1>

                <div className="space-y-3 text-gray-700 text-lg mb-6">
                    <ul className="list-disc list-inside space-y-2 text-gray-700 text-lg mb-6">
                        <li>Make sure you entered your email address correctly.</li>
                        <li>Check your spam folder to make sure our email didn’t end up there.</li>
                        <li>If you still can’t see it anywhere, please use the resend button.</li>
                        <li>
                            If you’re still having problems,{" "}
                            <a href="#" className="text-teal-600 hover:underline font-medium underline">Contact Support</a>
                        </li>
                    </ul>

                </div>

                <div className="mb-6">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter your email"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md text-lg bg-white focus:outline-none"
                    />
                    <p className="mt-1 text-sm text-gray-500">Double-check your email address above.</p>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    <button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-teal-600 hover:bg-teal-700 text-white py-2 rounded-md font-semibold disabled:opacity-60 flex items-center justify-center gap-2"
                    >
                        {isSubmitting && (
                            <AiOutlineLoading3Quarters className="animate-spin h-5 w-5" />
                        )}
                        {isSubmitting ? 'Sending...' : 'Resend Verification Code'}
                    </button>

                    <button
                        type="button"
                        onClick={() => navigate('/email-verify', { state: { email } })}
                        className="w-full hover:bg-gray-100 text-teal-700 py-2 rounded-md font-semibold border border-teal-600"
                    >
                        Back to Verification
                    </button>
                </form>
            </div>
        </div>
    );
};

export default NotReceiveMail;
