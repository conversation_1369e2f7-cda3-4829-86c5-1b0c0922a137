import '@testing-library/jest-dom';

// Mock react-toastify
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn()
  },
  ToastContainer: () => null
}));

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useParams: () => ({}),
  useLocation: () => ({
    pathname: '/',
    search: '',
    hash: '',
    state: null
  })
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock window.confirm
global.confirm = jest.fn(() => true);

// Mock window.alert
global.alert = jest.fn();

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
global.scrollTo = jest.fn();

// Mock fetch
global.fetch = jest.fn();

// Setup default fetch mock
beforeEach(() => {
  fetch.mockClear();
  localStorage.clear();
  sessionStorage.clear();
});

// Global test utilities
global.createMockUser = (overrides = {}) => ({
  id: 'user123',
  username: 'testuser',
  email: '<EMAIL>',
  profile_picture: 'avatar.jpg',
  ...overrides
});

global.createMockProduct = (overrides = {}) => ({
  _id: 'product123',
  title: 'Test Product',
  price: 100,
  brand: 'Test Brand',
  size: 'M',
  condition: 'New',
  product_photos: ['product1.jpg'],
  user: createMockUser(),
  ...overrides
});

global.createMockOrder = (overrides = {}) => ({
  _id: 'order123',
  orderNumber: 'ORD-1234567890-000001',
  status: 'pending_payment',
  createdAt: new Date().toISOString(),
  product: createMockProduct(),
  buyer: createMockUser({ username: 'buyer' }),
  seller: createMockUser({ username: 'seller' }),
  orderDetails: {
    productPrice: 100,
    quantity: 1,
    currency: 'USD'
  },
  payment: {
    method: 'escrow',
    status: 'pending',
    fees: {
      platformFee: 10,
      shippingFee: 5,
      tax: 0.72,
      total: 115.72
    }
  },
  timeline: [{
    status: 'pending_payment',
    timestamp: new Date().toISOString(),
    description: 'Order created',
    updatedBy: 'buyer'
  }],
  ...overrides
});

global.createMockShippingProvider = (overrides = {}) => ({
  _id: 'provider123',
  name: 'local_pickup',
  displayName: 'Local Pickup',
  isActive: true,
  supportedServices: [{
    serviceCode: 'LOCAL_PICKUP',
    serviceName: 'Local Pickup',
    estimatedDays: { min: 0, max: 1 },
    isActive: true
  }],
  pricing: {
    baseFee: 0,
    currency: 'AED'
  },
  ...overrides
});

global.createMockDeliveryOption = (overrides = {}) => ({
  _id: 'option123',
  user: 'user123',
  shippingProvider: createMockShippingProvider(),
  serviceCode: 'LOCAL_PICKUP',
  serviceName: 'Local Pickup',
  isDefault: true,
  preferences: {
    includeInsurance: false,
    requireSignature: false,
    allowCashOnDelivery: true
  },
  ...overrides
});

global.createMockTracking = (overrides = {}) => ({
  trackingNumber: 'TRACK123',
  status: 'in_transit',
  events: [{
    timestamp: new Date().toISOString(),
    status: 'shipped',
    description: 'Package shipped',
    location: { city: 'Dubai', country: 'AE' }
  }],
  estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
  provider: {
    displayName: 'Test Provider'
  },
  ...overrides
});

// Console suppression for tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
