const Chat = require('../../../../db/models/chatModel');
const Message = require('../../../../db/models/messageModel');
const Product = require('../../../../db/models/productModel');
const User = require('../../../../db/models/userModel');
const { successResponse, errorResponse } = require('../../../../utils/responseHandler');
const { createInitialSellerMessage } = require('../../../../utils/chatHelpers');
const mongoose = require('mongoose');

// Create or get existing chat for a product
exports.createOrGetChat = async (req, res) => {
  try {
    const { productId } = req.params;
    const buyerId = req.user._id;

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return errorResponse(res, 'Invalid product ID', 400);
    }

    // Get product details with full seller information
    const product = await Product.findById(productId).populate('user', 'userName firstName lastName profile city country lastLoginAt');
    if (!product) {
      return errorResponse(res, 'Product not found', 404);
    }

    const sellerId = product.user._id;

    // Check if user is trying to chat with themselves
    if (buyerId.toString() === sellerId.toString()) {
      return errorResponse(res, 'You cannot chat with yourself', 400);
    }

    // Check if chat already exists first
    const existingChat = await Chat.findOne({
      product: productId,
      buyer: buyerId,
      seller: sellerId
    }).populate('product', 'title user')
      .populate('buyer', 'userName firstName lastName profile')
      .populate('seller', 'userName firstName lastName profile')
      .populate('lastMessage');

    let chat;
    let isExisting = false;

    if (existingChat) {
      // Chat already exists - redirect to existing chat
      chat = existingChat;
      isExisting = true;
    } else {
      // Create new chat
      chat = await Chat.findOrCreateChat(productId, buyerId, sellerId);
      isExisting = false;

      // Create initial welcome message from seller for new chats
      const sellerName = product.user.firstName && product.user.lastName
        ? `${product.user.firstName} ${product.user.lastName}`
        : product.user.userName;

      // Format location
      const location = product.user.city && product.user.country
        ? `📍 ${product.user.country}, ${product.user.city}`
        : '📍 Location not specified';

      // Format last seen
      const getLastSeenText = (lastLoginAt) => {
        if (!lastLoginAt) return '👁 Last seen recently';

        const now = new Date();
        const lastLogin = new Date(lastLoginAt);
        const diffInMinutes = Math.floor((now - lastLogin) / (1000 * 60));

        if (diffInMinutes < 60) {
          return `� Last seen ${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
        } else if (diffInMinutes < 1440) { // Less than 24 hours
          const hours = Math.floor(diffInMinutes / 60);
          return `👁 Last seen ${hours} hour${hours !== 1 ? 's' : ''} ago`;
        } else {
          const days = Math.floor(diffInMinutes / 1440);
          return `👁 Last seen ${days} day${days !== 1 ? 's' : ''} ago`;
        }
      };

      const lastSeenText = getLastSeenText(product.user.lastLoginAt);

      const welcomeText = `Hi, I'm ${sellerName}

No reviews yet

${location}
${lastSeenText}`;

      // Create the initial message from seller using helper function
      const initialMessage = await createInitialSellerMessage(
        chat._id,
        sellerId,
        buyerId,
        product.user
      );

      // Update chat's last message to the initial message
      await chat.updateLastMessage(initialMessage._id);

      // Re-populate the chat with the new last message
      chat = await Chat.findById(chat._id)
        .populate('product', 'title user')
        .populate('buyer', 'userName firstName lastName profile')
        .populate('seller', 'userName firstName lastName profile')
        .populate('lastMessage');
    }

    const responseMessage = isExisting ? 'Existing chat found - redirecting to chat' : 'New chat created successfully';

    return successResponse(res, responseMessage, {
      chat: {
        id: chat._id,
        roomId: chat.roomId,
        isExisting: isExisting,
        product: {
          id: chat.product._id,
          title: chat.product.title,
          seller: {
            id: chat.seller._id,
            userName: chat.seller.userName,
            firstName: chat.seller.firstName,
            lastName: chat.seller.lastName,
            profile: chat.seller.profile
          }
        },
        buyer: {
          id: chat.buyer._id,
          userName: chat.buyer.userName,
          firstName: chat.buyer.firstName,
          lastName: chat.buyer.lastName,
          profile: chat.buyer.profile
        },
        lastMessage: chat.lastMessage,
        lastMessageAt: chat.lastMessageAt,
        createdAt: chat.createdAt
      }
    });

  } catch (error) {
    console.error('Create/Get chat error:', error);
    return errorResponse(res, 'Failed to create or get chat', 500, error.message);
  }
};

// Get all chats for a user
exports.getUserChats = async (req, res) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 20 } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const chats = await Chat.find({
      participants: userId,
      isActive: true
    })
    .populate('product', 'title product_photos price user')
    .populate('buyer', 'userName firstName lastName profile')
    .populate('seller', 'userName firstName lastName profile')
    .populate({
      path: 'lastMessage',
      populate: {
        path: 'sender',
        select: 'userName firstName lastName'
      }
    })
    .sort({ lastMessageAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

    // Get unread count for each chat
    const chatsWithUnreadCount = await Promise.all(
      chats.map(async (chat) => {
        const unreadCount = await Message.getUnreadCount(chat._id, userId);
        const otherUser = chat.buyer._id.toString() === userId.toString() ? chat.seller : chat.buyer;
        
        return {
          id: chat._id,
          roomId: chat.roomId,
          product: {
            id: chat.product._id,
            title: chat.product.title,
            photos: chat.product.product_photos,
            price: chat.product.price
          },
          otherUser: {
            id: otherUser._id,
            userName: otherUser.userName,
            firstName: otherUser.firstName,
            lastName: otherUser.lastName,
            profile: otherUser.profile
          },
          lastMessage: chat.lastMessage ? {
            id: chat.lastMessage._id,
            text: chat.lastMessage.text,
            messageType: chat.lastMessage.messageType,
            imageUrl: chat.lastMessage.imageUrl,
            sender: chat.lastMessage.sender,
            createdAt: chat.lastMessage.createdAt
          } : null,
          lastMessageAt: chat.lastMessageAt,
          unreadCount,
          createdAt: chat.createdAt
        };
      })
    );

    const total = await Chat.countDocuments({
      participants: userId,
      isActive: true
    });

    return successResponse(res, 'Chats retrieved successfully', {
      chats: chatsWithUnreadCount,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalChats: total,
        hasNextPage: skip + chats.length < total
      }
    });

  } catch (error) {
    console.error('Get user chats error:', error);
    return errorResponse(res, 'Failed to get chats', 500, error.message);
  }
};

// Get chat messages
exports.getChatMessages = async (req, res) => {
  try {
    const { chatId } = req.params;
    const userId = req.user._id;
    const { page = 1, limit = 50 } = req.query;

    // Validate chat ID
    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      return errorResponse(res, 'Invalid chat ID', 400);
    }

    // Check if user is participant in this chat
    const chat = await Chat.findOne({
      _id: chatId,
      participants: userId
    });

    if (!chat) {
      return errorResponse(res, 'Chat not found or access denied', 404);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const messages = await Message.find({ chat: chatId })
      .populate('sender', 'userName firstName lastName profile')
      .populate('receiver', 'userName firstName lastName profile')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Mark messages as seen
    await Message.markAsSeen(chatId, userId);

    const total = await Message.countDocuments({ chat: chatId });

    return successResponse(res, 'Messages retrieved successfully', {
      messages: messages.reverse().map(msg => ({
        id: msg._id,
        text: msg.text,
        messageType: msg.messageType,
        attachments: msg.attachments,
        // Include imageUrl for image messages
        imageUrl: msg.imageUrl ||
                 (msg.messageType === 'image' && msg.attachments?.[0]?.url
                   ? msg.attachments[0].url
                   : undefined),
        // Include offer data for offer messages
        offer: msg.offer,
        offerData: msg.offerData,
        sender: {
          id: msg.sender._id,
          userName: msg.sender.userName,
          firstName: msg.sender.firstName,
          lastName: msg.sender.lastName,
          profile: msg.sender.profile
        },
        receiver: {
          id: msg.receiver._id,
          userName: msg.receiver.userName,
          firstName: msg.receiver.firstName,
          lastName: msg.receiver.lastName,
          profile: msg.receiver.profile
        },
        seen: msg.seen,
        seenAt: msg.seenAt,
        status: msg.status,
        edited: msg.edited,
        editedAt: msg.editedAt,
        createdAt: msg.createdAt
      })),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalMessages: total,
        hasNextPage: skip + messages.length < total
      }
    });

  } catch (error) {
    console.error('Get chat messages error:', error);
    return errorResponse(res, 'Failed to get messages', 500, error.message);
  }
};

// Send message (HTTP endpoint - for fallback)
exports.sendMessage = async (req, res) => {
  try {
    const { chatId } = req.params;
    const { text, messageType = 'text' } = req.body;
    const senderId = req.user._id;

    // Validate inputs
    if (!text || !text.trim()) {
      return errorResponse(res, 'Message text is required', 400);
    }

    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      return errorResponse(res, 'Invalid chat ID', 400);
    }

    // Check if user is participant in this chat
    const chat = await Chat.findOne({
      _id: chatId,
      participants: senderId
    });

    if (!chat) {
      return errorResponse(res, 'Chat not found or access denied', 404);
    }

    // Determine receiver
    const receiverId = chat.participants.find(p => p.toString() !== senderId.toString());

    // Create message
    const message = await Message.create({
      chat: chatId,
      sender: senderId,
      receiver: receiverId,
      text: text.trim(),
      messageType
    });

    // Update chat's last message
    await chat.updateLastMessage(message._id);

    // Populate message for response
    const populatedMessage = await Message.findById(message._id)
      .populate('sender', 'userName firstName lastName profile')
      .populate('receiver', 'userName firstName lastName profile');

    return successResponse(res, 'Message sent successfully', {
      message: {
        id: populatedMessage._id,
        text: populatedMessage.text,
        messageType: populatedMessage.messageType,
        sender: {
          id: populatedMessage.sender._id,
          userName: populatedMessage.sender.userName,
          firstName: populatedMessage.sender.firstName,
          lastName: populatedMessage.sender.lastName,
          profile: populatedMessage.sender.profile
        },
        receiver: {
          id: populatedMessage.receiver._id,
          userName: populatedMessage.receiver.userName,
          firstName: populatedMessage.receiver.firstName,
          lastName: populatedMessage.receiver.lastName,
          profile: populatedMessage.receiver.profile
        },
        seen: populatedMessage.seen,
        status: populatedMessage.status,
        createdAt: populatedMessage.createdAt
      }
    });

  } catch (error) {
    console.error('Send message error:', error);
    return errorResponse(res, 'Failed to send message', 500, error.message);
  }
};

// Mark messages as seen
exports.markMessagesAsSeen = async (req, res) => {
  try {
    const { chatId } = req.params;
    const userId = req.user._id;

    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      return errorResponse(res, 'Invalid chat ID', 400);
    }

    // Check if user is participant in this chat
    const chat = await Chat.findOne({
      _id: chatId,
      participants: userId
    });

    if (!chat) {
      return errorResponse(res, 'Chat not found or access denied', 404);
    }

    const result = await Message.markAsSeen(chatId, userId);

    return successResponse(res, 'Messages marked as seen', {
      modifiedCount: result.modifiedCount
    });

  } catch (error) {
    console.error('Mark messages as seen error:', error);
    return errorResponse(res, 'Failed to mark messages as seen', 500, error.message);
  }
};
